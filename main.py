"""
ADEGuard Main Script

This script demonstrates the complete ADEGuard system pipeline:
1. Data loading and preprocessing
2. NER extraction for ADE and DRUG entities
3. Modifier-aware and age-specific clustering
4. Severity classification
5. Results analysis and visualization

Usage:
    python main.py [--sample-size N] [--output-dir DIR]
"""

import argparse
import sys
from pathlib import Path
import pandas as pd
import logging
from typing import Dict

# Add src directory to path
sys.path.append(str(Path(__file__).parent / "src"))

from preprocessing.data_loader import VAERSDataLoader
from labeling.ner_extractor import ADEDrugNER
from training.clustering import ModifierAwareClusterer
from training.severity_classifier import SeverityClassifier
from labeling.annotation_tool import AnnotationManager

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class ADEGuardPipeline:
    """
    Complete ADEGuard processing pipeline.
    
    This class orchestrates the entire ADE detection and analysis workflow
    following the annotation guidelines and project requirements.
    """
    
    def __init__(self, data_dir: str = "data/raw", output_dir: str = "data/processed"):
        """
        Initialize the ADEGuard pipeline.
        
        Args:
            data_dir: Directory containing raw VAERS data
            output_dir: Directory for processed outputs
        """
        self.data_dir = Path(data_dir)
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # Initialize components
        self.data_loader = VAERSDataLoader(str(self.data_dir))
        self.ner_extractor = ADEDrugNER()
        self.clusterer = ModifierAwareClusterer()
        self.classifier = SeverityClassifier()
        self.annotation_manager = AnnotationManager()
        
        # Pipeline results
        self.raw_data = None
        self.processed_data = None
        self.entities_data = None
        self.clustered_data = None
        self.classified_data = None
    
    def run_complete_pipeline(self, sample_size: int = 100) -> Dict[str, pd.DataFrame]:
        """
        Run the complete ADEGuard pipeline.
        
        Args:
            sample_size: Number of records to process
            
        Returns:
            Dictionary containing all pipeline results
        """
        logger.info("Starting ADEGuard complete pipeline...")
        
        # Step 1: Data Loading and Preprocessing
        logger.info("Step 1: Loading and preprocessing data...")
        try:
            self.processed_data = self.data_loader.merge_datasets()
        except FileNotFoundError as e:
            logger.error(f"Data files not found: {e}")
            logger.info("Please ensure VAERS data files are in data/raw/ directory")
            return self._create_results_dict()
        except UnicodeDecodeError as e:
            logger.error(f"Encoding error: {e}")
            logger.info("The system will try multiple encodings automatically")
            return self._create_results_dict()
        
        # Get sample for processing
        sample_data = self.data_loader.get_sample_data(n_samples=sample_size)
        logger.info(f"Processing {len(sample_data)} records")
        
        # Step 2: NER Entity Extraction
        logger.info("Step 2: Extracting ADE and DRUG entities...")
        self.entities_data = self.ner_extractor.process_dataframe(sample_data)
        
        if self.entities_data.empty:
            logger.warning("No entities extracted. Pipeline stopped.")
            return self._create_results_dict()
        
        logger.info(f"Extracted {len(self.entities_data)} entities")
        
        # Step 3: Clustering Analysis
        logger.info("Step 3: Performing modifier-aware clustering...")
        clustering_df = self.clusterer.prepare_clustering_data(self.entities_data)
        
        if not clustering_df.empty:
            self.clustered_data = self.clusterer.perform_clustering(clustering_df)
            logger.info(f"Created {len(self.clustered_data['cluster_label'].unique())} clusters")
        else:
            logger.warning("No data available for clustering")
            self.clustered_data = pd.DataFrame()
        
        # Step 4: Severity Classification
        logger.info("Step 4: Classifying severity...")
        ade_entities = self.entities_data[self.entities_data['ENTITY_LABEL'] == 'ADE']
        
        if not ade_entities.empty:
            self.classified_data = self.classifier.classify_severity_hybrid(ade_entities)
            logger.info(f"Classified {len(self.classified_data)} ADE entities")
        else:
            logger.warning("No ADE entities found for classification")
            self.classified_data = pd.DataFrame()
        
        # Step 5: Save Results
        logger.info("Step 5: Saving results...")
        self._save_results()
        
        # Step 6: Generate Analysis Report
        logger.info("Step 6: Generating analysis report...")
        self._generate_analysis_report()
        
        logger.info("ADEGuard pipeline completed successfully!")
        
        return self._create_results_dict()
    
    def _create_results_dict(self) -> Dict[str, pd.DataFrame]:
        """Create dictionary of all pipeline results."""
        return {
            'processed_data': self.processed_data if self.processed_data is not None else pd.DataFrame(),
            'entities_data': self.entities_data if self.entities_data is not None else pd.DataFrame(),
            'clustered_data': self.clustered_data if self.clustered_data is not None else pd.DataFrame(),
            'classified_data': self.classified_data if self.classified_data is not None else pd.DataFrame()
        }
    
    def _save_results(self):
        """Save all pipeline results to files."""
        
        # Save processed data
        if self.processed_data is not None:
            self.processed_data.to_csv(self.output_dir / "vaers_processed.csv", index=False)
            logger.info("Saved processed data")
        
        # Save extracted entities
        if self.entities_data is not None and not self.entities_data.empty:
            self.entities_data.to_csv(self.output_dir / "extracted_entities.csv", index=False)
            logger.info("Saved extracted entities")
        
        # Save clustering results
        if self.clustered_data is not None and not self.clustered_data.empty:
            self.clustered_data.to_csv(self.output_dir / "clustered_entities.csv", index=False)
            logger.info("Saved clustering results")
        
        # Save classification results
        if self.classified_data is not None and not self.classified_data.empty:
            self.classified_data.to_csv(self.output_dir / "classified_entities.csv", index=False)
            logger.info("Saved classification results")
    
    def _generate_analysis_report(self):
        """Generate comprehensive analysis report."""
        
        report_lines = []
        report_lines.append("=" * 60)
        report_lines.append("ADEGuard Analysis Report")
        report_lines.append("=" * 60)
        report_lines.append("")
        
        # Data Overview
        if self.processed_data is not None:
            report_lines.append("DATA OVERVIEW:")
            report_lines.append(f"- Total records processed: {len(self.processed_data)}")
            report_lines.append(f"- Age group distribution:")
            age_dist = self.processed_data['AGE_GROUP'].value_counts()
            for age, count in age_dist.items():
                report_lines.append(f"  * {age}: {count}")
            report_lines.append("")
        
        # Entity Extraction Results
        if self.entities_data is not None and not self.entities_data.empty:
            report_lines.append("ENTITY EXTRACTION RESULTS:")
            report_lines.append(f"- Total entities extracted: {len(self.entities_data)}")
            
            entity_dist = self.entities_data['ENTITY_LABEL'].value_counts()
            for label, count in entity_dist.items():
                report_lines.append(f"  * {label}: {count}")
            
            avg_confidence = self.entities_data['ENTITY_CONFIDENCE'].mean()
            report_lines.append(f"- Average confidence: {avg_confidence:.3f}")
            report_lines.append("")
        
        # Clustering Results
        if self.clustered_data is not None and not self.clustered_data.empty:
            report_lines.append("CLUSTERING RESULTS:")
            unique_clusters = self.clustered_data['cluster_label'].unique()
            non_noise_clusters = [c for c in unique_clusters if c != 'noise']
            report_lines.append(f"- Total clusters found: {len(non_noise_clusters)}")
            report_lines.append(f"- Noise entities: {len(self.clustered_data[self.clustered_data['cluster_label'] == 'noise'])}")
            
            # Top clusters by size
            cluster_sizes = self.clustered_data['cluster_label'].value_counts()
            report_lines.append("- Top 5 clusters by size:")
            for cluster, size in cluster_sizes.head(5).items():
                report_lines.append(f"  * {cluster}: {size} entities")
            report_lines.append("")
        
        # Severity Classification Results
        if self.classified_data is not None and not self.classified_data.empty:
            report_lines.append("SEVERITY CLASSIFICATION RESULTS:")
            report_lines.append(f"- Total ADE entities classified: {len(self.classified_data)}")
            
            severity_dist = self.classified_data['final_severity'].value_counts()
            for severity, count in severity_dist.items():
                report_lines.append(f"  * {severity.title()}: {count}")
            
            avg_confidence = self.classified_data['final_confidence'].mean()
            report_lines.append(f"- Average classification confidence: {avg_confidence:.3f}")
            
            # High-severity cases
            high_severity = self.classified_data[
                self.classified_data['final_severity'].isin(['severe', 'critical'])
            ]
            report_lines.append(f"- High-severity cases: {len(high_severity)}")
            report_lines.append("")
        
        # Key Insights
        report_lines.append("KEY INSIGHTS:")
        
        if self.entities_data is not None and not self.entities_data.empty:
            # Most common ADE terms
            ade_entities = self.entities_data[self.entities_data['ENTITY_LABEL'] == 'ADE']
            if not ade_entities.empty:
                common_ades = ade_entities['ENTITY_TEXT'].value_counts().head(5)
                report_lines.append("- Most common ADEs:")
                for ade, count in common_ades.items():
                    report_lines.append(f"  * {ade}: {count} occurrences")
            
            # Most common drugs
            drug_entities = self.entities_data[self.entities_data['ENTITY_LABEL'] == 'DRUG']
            if not drug_entities.empty:
                common_drugs = drug_entities['ENTITY_TEXT'].value_counts().head(5)
                report_lines.append("- Most common drugs/vaccines:")
                for drug, count in common_drugs.items():
                    report_lines.append(f"  * {drug}: {count} occurrences")
        
        report_lines.append("")
        report_lines.append("=" * 60)
        
        # Save report
        report_text = "\n".join(report_lines)
        with open(self.output_dir / "analysis_report.txt", "w") as f:
            f.write(report_text)
        
        # Print report to console
        print(report_text)
    
    def create_gold_standard_annotations(self, n_samples: int = 10) -> str:
        """
        Create a sample annotation project for gold standard data.
        
        Args:
            n_samples: Number of documents to include
            
        Returns:
            Project ID
        """
        logger.info(f"Creating gold standard annotation project with {n_samples} samples...")
        
        # Get sample data
        if self.processed_data is None:
            self.processed_data = self.data_loader.merge_datasets()
        
        sample_data = self.data_loader.get_sample_data(n_samples=n_samples)
        
        # Prepare documents for annotation
        documents = []
        for _, row in sample_data.iterrows():
            doc = {
                'text': row['SYMPTOM_TEXT_CLEAN'],
                'metadata': {
                    'vaers_id': row['VAERS_ID'],
                    'age_group': row['AGE_GROUP'],
                    'severity_score': row['SEVERITY_SCORE']
                }
            }
            documents.append(doc)
        
        # Create annotation project
        project_id = self.annotation_manager.create_annotation_project(
            "ADEGuard_Gold_Standard", 
            documents
        )
        
        logger.info(f"Created annotation project: {project_id}")
        return project_id


def main():
    """Main function to run ADEGuard pipeline."""
    
    parser = argparse.ArgumentParser(description="ADEGuard - AI-Powered ADE Detection System")
    parser.add_argument("--sample-size", type=int, default=100, 
                       help="Number of records to process (default: 100)")
    parser.add_argument("--output-dir", type=str, default="data/processed",
                       help="Output directory for results (default: data/processed)")
    parser.add_argument("--create-annotations", action="store_true",
                       help="Create sample annotation project")
    parser.add_argument("--annotation-samples", type=int, default=10,
                       help="Number of samples for annotation project (default: 10)")
    
    args = parser.parse_args()
    
    print("🏥 ADEGuard - AI-Powered ADE Detection System")
    print("=" * 50)
    
    try:
        # Initialize pipeline
        pipeline = ADEGuardPipeline(output_dir=args.output_dir)
        
        # Run complete pipeline
        results = pipeline.run_complete_pipeline(sample_size=args.sample_size)
        
        # Create annotation project if requested
        if args.create_annotations:
            project_id = pipeline.create_gold_standard_annotations(args.annotation_samples)
            print(f"\n📝 Created annotation project: {project_id}")
        
        print(f"\n✅ Pipeline completed successfully!")
        print(f"📁 Results saved to: {args.output_dir}")
        print(f"📊 Analysis report: {args.output_dir}/analysis_report.txt")
        
        # Print quick summary
        print("\n📈 Quick Summary:")
        if not results['entities_data'].empty:
            entity_counts = results['entities_data']['ENTITY_LABEL'].value_counts()
            for label, count in entity_counts.items():
                print(f"   {label}: {count} entities")
        
        if not results['classified_data'].empty:
            severity_counts = results['classified_data']['final_severity'].value_counts()
            print(f"   Severity distribution: {dict(severity_counts)}")
        
        print("\n🚀 To launch the interactive interface, run:")
        print("   streamlit run streamlit_app.py")
        
    except Exception as e:
        logger.error(f"Pipeline failed: {e}")
        print(f"❌ Error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
