"""
ADEGuard Annotation Tool

This module provides tools for creating gold standard annotations
following the VAERS annotation guidelines.
"""

import pandas as pd
import numpy as np
import json
import re
from typing import List, Dict, Tuple, Optional
import logging
from pathlib import Path
from dataclasses import dataclass, asdict
from datetime import datetime

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@dataclass
class Annotation:
    """Represents a single annotation."""
    text: str
    start: int
    end: int
    label: str
    annotator: str
    timestamp: str
    confidence: float = 1.0
    notes: str = ""


@dataclass
class AnnotatedDocument:
    """Represents a document with annotations."""
    doc_id: str
    text: str
    annotations: List[Annotation]
    metadata: Dict
    annotator: str
    timestamp: str
    status: str = "in_progress"  # in_progress, completed, reviewed


class AnnotationValidator:
    """
    Validates annotations according to VAERS annotation guidelines.
    """
    
    def __init__(self):
        """Initialize the validator with annotation rules."""
        self._init_validation_rules()
    
    def _init_validation_rules(self):
        """Initialize validation rules based on annotation guidelines."""
        
        # ADE validation patterns
        self.ade_valid_patterns = [
            r'\b(?:severe|mild|moderate|acute|chronic|extreme)\s+(?:headache|fever|pain|fatigue|nausea|dizziness)\b',
            r'\b(?:shortness of breath|difficulty breathing|chest pain|abdominal pain)\b',
            r'\b(?:injection site\s+(?:pain|swelling|redness|reaction))\b',
            r'\b(?:allergic reaction|anaphylaxis|hypersensitivity)\b',
            r'\b(?:myocarditis|pericarditis|thrombosis|embolism)\b',
            r'\b(?:seizure|convulsion|syncope|fainting)\b',
            r'\b(?:rash|urticaria|hives|erythema|swelling)\b'
        ]
        
        # ADE invalid patterns (should be excluded)
        self.ade_invalid_patterns = [
            r'\b(?:not feeling well|general malaise)\b',  # Too vague
            r'\b(?:product dose omission|administration error)\b',  # Administrative only
            r'\b(?:history of|past medical history)\b',  # Pre-existing
            r'\b(?:for \d+ days?|lasting \d+ hours?)\b',  # Duration should be excluded
        ]
        
        # DRUG validation patterns
        self.drug_valid_patterns = [
            r'\b(?:Pfizer|Moderna|Johnson\s*&?\s*Johnson|J&J)\s*(?:COVID-19\s*)?(?:vaccine)?\b',
            r'\b(?:Shingrix|Zostavax|Fluzone|FluMist)\b',
            r'\b(?:COVID-19\s+vaccine|coronavirus\s+vaccine)\b',
            r'\b(?:mRNA\s+vaccine|viral\s+vector\s+vaccine)\b',
            r'\b(?:booster\s+shot|booster\s+dose)\b'
        ]
        
        # DRUG invalid patterns
        self.drug_invalid_patterns = [
            r'\b(?:discontinued|stopped|past use|previously)\b',  # Past medication
        ]
        
        # Compile patterns
        self.ade_valid_regex = [re.compile(p, re.IGNORECASE) for p in self.ade_valid_patterns]
        self.ade_invalid_regex = [re.compile(p, re.IGNORECASE) for p in self.ade_invalid_patterns]
        self.drug_valid_regex = [re.compile(p, re.IGNORECASE) for p in self.drug_valid_patterns]
        self.drug_invalid_regex = [re.compile(p, re.IGNORECASE) for p in self.drug_invalid_patterns]
    
    def validate_annotation(self, annotation: Annotation, full_text: str) -> Dict[str, any]:
        """
        Validate a single annotation according to guidelines.
        
        Args:
            annotation: Annotation to validate
            full_text: Full document text for context
            
        Returns:
            Validation result with warnings and suggestions
        """
        result = {
            'is_valid': True,
            'warnings': [],
            'suggestions': [],
            'confidence': 1.0
        }
        
        text = annotation.text.lower()
        label = annotation.label.upper()
        
        # Check annotation length
        if len(annotation.text) < 2:
            result['warnings'].append("Annotation is very short (< 2 characters)")
            result['confidence'] *= 0.7
        
        # Validate ADE annotations
        if label == 'ADE':
            result.update(self._validate_ade_annotation(annotation, full_text))
        
        # Validate DRUG annotations
        elif label == 'DRUG':
            result.update(self._validate_drug_annotation(annotation, full_text))
        
        # Check for overlapping annotations (would need list of all annotations)
        # This would be implemented in the annotation interface
        
        return result
    
    def _validate_ade_annotation(self, annotation: Annotation, full_text: str) -> Dict[str, any]:
        """Validate ADE annotation specifically."""
        result = {'warnings': [], 'suggestions': []}
        text = annotation.text.lower()
        
        # Check for invalid patterns
        for pattern in self.ade_invalid_regex:
            if pattern.search(text):
                result['warnings'].append(f"Contains pattern that should be excluded: {pattern.pattern}")
        
        # Check if it's part of a vaccine name
        context_start = max(0, annotation.start - 30)
        context_end = min(len(full_text), annotation.end + 30)
        context = full_text[context_start:context_end].lower()
        
        if "covid-19" in text and "vaccine" in context:
            result['warnings'].append("COVID-19 might be part of vaccine name, not an ADE")
        
        # Check for duration in the annotation
        duration_pattern = re.compile(r'\b(?:for|lasting)\s+\d+\s+(?:days?|hours?|weeks?)\b', re.IGNORECASE)
        if duration_pattern.search(text):
            result['suggestions'].append("Consider excluding duration from ADE span")
        
        # Check for severity modifiers
        severity_pattern = re.compile(r'\b(?:severe|mild|moderate|acute|chronic)\b', re.IGNORECASE)
        if severity_pattern.search(text):
            result['suggestions'].append("Good: includes severity modifier as per guidelines")
        
        return result
    
    def _validate_drug_annotation(self, annotation: Annotation, full_text: str) -> Dict[str, any]:
        """Validate DRUG annotation specifically."""
        result = {'warnings': [], 'suggestions': []}
        text = annotation.text.lower()
        
        # Check for invalid patterns
        for pattern in self.drug_invalid_regex:
            if pattern.search(text):
                result['warnings'].append(f"Contains pattern suggesting past use: {pattern.pattern}")
        
        # Check if it's a valid drug/vaccine name
        is_valid_drug = False
        for pattern in self.drug_valid_regex:
            if pattern.search(text):
                is_valid_drug = True
                break
        
        if not is_valid_drug and len(text) > 3:
            result['suggestions'].append("Verify this is a valid drug/vaccine name")
        
        return result


class AnnotationManager:
    """
    Manages annotation projects and data.
    """
    
    def __init__(self, project_dir: str = "data/annotations"):
        """
        Initialize annotation manager.
        
        Args:
            project_dir: Directory to store annotation projects
        """
        self.project_dir = Path(project_dir)
        self.project_dir.mkdir(parents=True, exist_ok=True)
        self.validator = AnnotationValidator()
    
    def create_annotation_project(self, project_name: str, documents: List[Dict]) -> str:
        """
        Create a new annotation project.
        
        Args:
            project_name: Name of the project
            documents: List of documents to annotate
            
        Returns:
            Project ID
        """
        project_id = f"{project_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        project_path = self.project_dir / project_id
        project_path.mkdir(exist_ok=True)
        
        # Save project metadata
        metadata = {
            'project_id': project_id,
            'project_name': project_name,
            'created_at': datetime.now().isoformat(),
            'total_documents': len(documents),
            'annotation_guidelines': 'VAERS ADE & DRUG Annotation Guidelines',
            'status': 'active'
        }
        
        with open(project_path / 'metadata.json', 'w') as f:
            json.dump(metadata, f, indent=2)
        
        # Save documents
        documents_data = []
        for i, doc in enumerate(documents):
            doc_id = f"{project_id}_doc_{i:04d}"
            annotated_doc = AnnotatedDocument(
                doc_id=doc_id,
                text=doc.get('text', ''),
                annotations=[],
                metadata=doc.get('metadata', {}),
                annotator="",
                timestamp=datetime.now().isoformat()
            )
            documents_data.append(asdict(annotated_doc))
        
        with open(project_path / 'documents.json', 'w') as f:
            json.dump(documents_data, f, indent=2)
        
        logger.info(f"Created annotation project: {project_id}")
        return project_id
    
    def load_project(self, project_id: str) -> Dict:
        """Load an annotation project."""
        project_path = self.project_dir / project_id
        
        if not project_path.exists():
            raise ValueError(f"Project {project_id} not found")
        
        # Load metadata
        with open(project_path / 'metadata.json', 'r') as f:
            metadata = json.load(f)
        
        # Load documents
        with open(project_path / 'documents.json', 'r') as f:
            documents = json.load(f)
        
        return {
            'metadata': metadata,
            'documents': documents
        }
    
    def save_annotations(self, project_id: str, doc_id: str, annotations: List[Annotation], 
                        annotator: str) -> bool:
        """
        Save annotations for a document.
        
        Args:
            project_id: Project identifier
            doc_id: Document identifier
            annotations: List of annotations
            annotator: Annotator name
            
        Returns:
            Success status
        """
        try:
            project_path = self.project_dir / project_id
            
            # Load current documents
            with open(project_path / 'documents.json', 'r') as f:
                documents = json.load(f)
            
            # Find and update the document
            for doc in documents:
                if doc['doc_id'] == doc_id:
                    doc['annotations'] = [asdict(ann) for ann in annotations]
                    doc['annotator'] = annotator
                    doc['timestamp'] = datetime.now().isoformat()
                    doc['status'] = 'completed'
                    break
            
            # Save updated documents
            with open(project_path / 'documents.json', 'w') as f:
                json.dump(documents, f, indent=2)
            
            logger.info(f"Saved annotations for {doc_id} by {annotator}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to save annotations: {e}")
            return False
    
    def export_annotations(self, project_id: str, format: str = 'json') -> str:
        """
        Export annotations in various formats.
        
        Args:
            project_id: Project identifier
            format: Export format ('json', 'csv', 'conll')
            
        Returns:
            Path to exported file
        """
        project_data = self.load_project(project_id)
        project_path = self.project_dir / project_id
        
        if format == 'json':
            export_path = project_path / f"{project_id}_annotations.json"
            with open(export_path, 'w') as f:
                json.dump(project_data, f, indent=2)
        
        elif format == 'csv':
            export_path = project_path / f"{project_id}_annotations.csv"
            
            # Flatten annotations to CSV format
            rows = []
            for doc in project_data['documents']:
                for ann in doc['annotations']:
                    rows.append({
                        'doc_id': doc['doc_id'],
                        'text': doc['text'],
                        'annotation_text': ann['text'],
                        'start': ann['start'],
                        'end': ann['end'],
                        'label': ann['label'],
                        'annotator': ann['annotator'],
                        'timestamp': ann['timestamp'],
                        'confidence': ann['confidence'],
                        'notes': ann['notes']
                    })
            
            df = pd.DataFrame(rows)
            df.to_csv(export_path, index=False)
        
        elif format == 'conll':
            export_path = project_path / f"{project_id}_annotations.conll"
            
            # Convert to CoNLL format
            with open(export_path, 'w') as f:
                for doc in project_data['documents']:
                    text = doc['text']
                    annotations = doc['annotations']
                    
                    # Create token-level labels
                    tokens = text.split()
                    labels = ['O'] * len(tokens)
                    
                    # Map annotations to tokens (simplified)
                    for ann in annotations:
                        # This is a simplified mapping - in practice, you'd need
                        # more sophisticated tokenization alignment
                        start_token = len(text[:ann['start']].split())
                        end_token = len(text[:ann['end']].split())
                        
                        for i in range(start_token, min(end_token, len(labels))):
                            if i == start_token:
                                labels[i] = f"B-{ann['label']}"
                            else:
                                labels[i] = f"I-{ann['label']}"
                    
                    # Write tokens and labels
                    for token, label in zip(tokens, labels):
                        f.write(f"{token}\t{label}\n")
                    f.write("\n")  # Empty line between documents
        
        logger.info(f"Exported annotations to {export_path}")
        return str(export_path)
    
    def get_annotation_statistics(self, project_id: str) -> Dict:
        """Get statistics for an annotation project."""
        project_data = self.load_project(project_id)
        
        stats = {
            'total_documents': len(project_data['documents']),
            'completed_documents': 0,
            'total_annotations': 0,
            'ade_annotations': 0,
            'drug_annotations': 0,
            'annotators': set(),
            'avg_annotations_per_doc': 0
        }
        
        for doc in project_data['documents']:
            if doc['status'] == 'completed':
                stats['completed_documents'] += 1
            
            annotations = doc['annotations']
            stats['total_annotations'] += len(annotations)
            stats['annotators'].add(doc['annotator'])
            
            for ann in annotations:
                if ann['label'] == 'ADE':
                    stats['ade_annotations'] += 1
                elif ann['label'] == 'DRUG':
                    stats['drug_annotations'] += 1
        
        if stats['completed_documents'] > 0:
            stats['avg_annotations_per_doc'] = stats['total_annotations'] / stats['completed_documents']
        
        stats['annotators'] = list(stats['annotators'])
        
        return stats


def create_sample_annotation_project():
    """Create a sample annotation project for demonstration."""
    
    # Sample documents from VAERS data
    sample_documents = [
        {
            'text': "Patient received Pfizer COVID-19 vaccine and experienced severe headache, injection site pain, and mild fever lasting 2 days.",
            'metadata': {'vaers_id': 'SAMPLE_001', 'age_group': 'Adult (18-65)'}
        },
        {
            'text': "After Moderna vaccination, patient developed rash and shortness of breath. Emergency room visit required.",
            'metadata': {'vaers_id': 'SAMPLE_002', 'age_group': 'Adult (18-65)'}
        },
        {
            'text': "Elderly patient received Shingrix vaccine. Experienced mild arm soreness but no other adverse events.",
            'metadata': {'vaers_id': 'SAMPLE_003', 'age_group': 'Elderly (65+)'}
        }
    ]
    
    # Create annotation manager and project
    manager = AnnotationManager()
    project_id = manager.create_annotation_project("VAERS_Sample_Annotation", sample_documents)
    
    print(f"Created sample annotation project: {project_id}")
    
    # Create sample annotations for first document
    sample_annotations = [
        Annotation(
            text="Pfizer COVID-19 vaccine",
            start=16,
            end=40,
            label="DRUG",
            annotator="sample_annotator",
            timestamp=datetime.now().isoformat(),
            confidence=1.0,
            notes="Clear vaccine name"
        ),
        Annotation(
            text="severe headache",
            start=57,
            end=72,
            label="ADE",
            annotator="sample_annotator",
            timestamp=datetime.now().isoformat(),
            confidence=1.0,
            notes="Includes severity modifier"
        ),
        Annotation(
            text="injection site pain",
            start=74,
            end=93,
            label="ADE",
            annotator="sample_annotator",
            timestamp=datetime.now().isoformat(),
            confidence=1.0,
            notes="Common injection site reaction"
        ),
        Annotation(
            text="mild fever",
            start=99,
            end=109,
            label="ADE",
            annotator="sample_annotator",
            timestamp=datetime.now().isoformat(),
            confidence=1.0,
            notes="Includes severity modifier"
        )
    ]
    
    # Save sample annotations
    manager.save_annotations(project_id, f"{project_id}_doc_0000", sample_annotations, "sample_annotator")
    
    # Get statistics
    stats = manager.get_annotation_statistics(project_id)
    print("Project statistics:", stats)
    
    # Export annotations
    export_path = manager.export_annotations(project_id, format='csv')
    print(f"Exported annotations to: {export_path}")
    
    return project_id


if __name__ == "__main__":
    # Create sample project
    project_id = create_sample_annotation_project()
    
    # Demonstrate validation
    validator = AnnotationValidator()
    
    sample_annotation = Annotation(
        text="severe headache for 3 days",
        start=0,
        end=25,
        label="ADE",
        annotator="test",
        timestamp=datetime.now().isoformat()
    )
    
    validation_result = validator.validate_annotation(
        sample_annotation, 
        "Patient experienced severe headache for 3 days after vaccination"
    )
    
    print("Validation result:", validation_result)
