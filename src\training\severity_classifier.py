"""
ADEGuard Severity Classification Module

This module implements rule-based and ML-based severity classification for ADEs
using clinical indicators, text analysis, and BioBERT classification.
"""

import pandas as pd
import numpy as np
import re
from typing import List, Dict, Tuple, Optional
import logging
from sklearn.ensemble import RandomForestClassifier
from sklearn.model_selection import train_test_split
from sklearn.metrics import classification_report, confusion_matrix
from sklearn.preprocessing import LabelEncoder
from transformers import AutoTokenizer, AutoModelForSequenceClassification, pipeline
import joblib

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class SeverityClassifier:
    """
    Severity classification system for ADE entities.
    
    This class implements:
    - Rule-based severity classification using clinical indicators
    - ML-based classification using text features
    - BioBERT-based severity prediction
    - Hybrid approach combining multiple methods
    """
    
    def __init__(self, model_name: str = "dmis-lab/biobert-base-cased-v1.1"):
        """
        Initialize the severity classifier.
        
        Args:
            model_name: HuggingFace model name for biomedical classification
        """
        self.model_name = model_name
        self.biobert_pipeline = None
        self.ml_classifier = None
        self.label_encoder = LabelEncoder()
        
        # Initialize rule-based patterns
        self._init_severity_rules()
        
        # Try to load BioBERT model
        self._load_biobert_model()
    
    def _init_severity_rules(self):
        """Initialize rule-based severity classification patterns."""
        
        # Severity indicators based on clinical outcomes
        self.severity_rules = {
            'critical': {
                'outcomes': ['death', 'died', 'fatal', 'mortality', 'life-threatening'],
                'symptoms': [
                    'cardiac arrest', 'respiratory failure', 'anaphylactic shock',
                    'severe anaphylaxis', 'status epilepticus', 'coma',
                    'acute respiratory distress syndrome', 'septic shock'
                ],
                'modifiers': ['life-threatening', 'critical', 'fatal', 'severe'],
                'score_threshold': 4  # Based on SEVERITY_SCORE from data
            },
            'severe': {
                'outcomes': ['hospitalization', 'emergency', 'intensive care'],
                'symptoms': [
                    'myocarditis', 'pericarditis', 'thrombosis', 'embolism',
                    'seizure', 'stroke', 'heart attack', 'anaphylaxis',
                    'severe allergic reaction', 'paralysis', 'blindness'
                ],
                'modifiers': ['severe', 'acute', 'intense', 'extreme', 'significant'],
                'score_threshold': 2
            },
            'moderate': {
                'outcomes': ['emergency room visit', 'medical attention'],
                'symptoms': [
                    'persistent fever', 'prolonged headache', 'chest pain',
                    'difficulty breathing', 'severe rash', 'joint pain',
                    'muscle weakness', 'vision problems'
                ],
                'modifiers': ['moderate', 'persistent', 'prolonged', 'significant'],
                'score_threshold': 1
            },
            'mild': {
                'outcomes': ['outpatient visit', 'self-limiting'],
                'symptoms': [
                    'injection site pain', 'mild fever', 'headache', 'fatigue',
                    'muscle aches', 'mild rash', 'nausea', 'dizziness'
                ],
                'modifiers': ['mild', 'slight', 'minor', 'transient', 'brief'],
                'score_threshold': 0
            }
        }
        
        # Compile regex patterns
        self.severity_patterns = {}
        for severity, rules in self.severity_rules.items():
            patterns = []
            
            # Add outcome patterns
            for outcome in rules['outcomes']:
                patterns.append(rf'\b{re.escape(outcome)}\b')
            
            # Add symptom patterns
            for symptom in rules['symptoms']:
                patterns.append(rf'\b{re.escape(symptom)}\b')
            
            # Add modifier patterns
            for modifier in rules['modifiers']:
                patterns.append(rf'\b{re.escape(modifier)}\b')
            
            self.severity_patterns[severity] = [
                re.compile(pattern, re.IGNORECASE) for pattern in patterns
            ]
        
        # Age-specific severity adjustments
        self.age_severity_adjustments = {
            'Infant (0-2)': {'multiplier': 1.5, 'threshold_adjustment': -0.5},
            'Child (2-12)': {'multiplier': 1.3, 'threshold_adjustment': -0.3},
            'Adolescent (12-18)': {'multiplier': 1.1, 'threshold_adjustment': -0.1},
            'Adult (18-65)': {'multiplier': 1.0, 'threshold_adjustment': 0.0},
            'Elderly (65+)': {'multiplier': 1.4, 'threshold_adjustment': -0.4}
        }
    
    def _load_biobert_model(self):
        """Load BioBERT model for severity classification."""
        try:
            logger.info("Loading BioBERT model for severity classification...")
            # Note: In practice, you'd need a model fine-tuned for severity classification
            # For now, we'll use a general classification pipeline
            self.biobert_pipeline = pipeline(
                "text-classification",
                model=self.model_name,
                return_all_scores=True
            )
            logger.info("BioBERT model loaded successfully")
        except Exception as e:
            logger.warning(f"Failed to load BioBERT model: {e}")
            logger.info("Will use rule-based and ML classification only")
    
    def classify_severity_rules(self, text: str, age_group: str = 'Adult (18-65)', 
                               severity_score: int = 0) -> Dict[str, any]:
        """
        Classify severity using rule-based approach.
        
        Args:
            text: ADE text to classify
            age_group: Patient age group
            severity_score: Clinical severity score from data
            
        Returns:
            Dictionary with severity classification results
        """
        text_lower = text.lower()
        
        # Initialize scores for each severity level
        severity_scores = {
            'critical': 0,
            'severe': 0,
            'moderate': 0,
            'mild': 0
        }
        
        # Check patterns for each severity level
        for severity, patterns in self.severity_patterns.items():
            for pattern in patterns:
                if pattern.search(text):
                    severity_scores[severity] += 1
        
        # Apply clinical severity score
        if severity_score >= self.severity_rules['critical']['score_threshold']:
            severity_scores['critical'] += 3
        elif severity_score >= self.severity_rules['severe']['score_threshold']:
            severity_scores['severe'] += 2
        elif severity_score >= self.severity_rules['moderate']['score_threshold']:
            severity_scores['moderate'] += 1
        else:
            severity_scores['mild'] += 1
        
        # Apply age-specific adjustments
        if age_group in self.age_severity_adjustments:
            adjustment = self.age_severity_adjustments[age_group]
            multiplier = adjustment['multiplier']
            
            # Increase severity for vulnerable age groups
            if multiplier > 1.0:
                severity_scores['severe'] = int(severity_scores['severe'] * multiplier)
                severity_scores['critical'] = int(severity_scores['critical'] * multiplier)
        
        # Determine final severity
        max_severity = max(severity_scores, key=severity_scores.get)
        max_score = severity_scores[max_severity]
        
        # If no clear indicators, default based on clinical score
        if max_score == 0:
            if severity_score >= 2:
                max_severity = 'severe'
            elif severity_score >= 1:
                max_severity = 'moderate'
            else:
                max_severity = 'mild'
        
        return {
            'predicted_severity': max_severity,
            'confidence': max_score / (sum(severity_scores.values()) + 1e-6),
            'severity_scores': severity_scores,
            'method': 'rule_based'
        }
    
    def extract_severity_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Extract features for ML-based severity classification.
        
        Args:
            df: DataFrame with ADE data
            
        Returns:
            DataFrame with extracted features
        """
        features_df = df.copy()
        
        # Text-based features
        features_df['text_length'] = features_df['ENTITY_TEXT'].str.len()
        features_df['word_count'] = features_df['ENTITY_TEXT'].str.split().str.len()
        
        # Clinical outcome features
        outcome_cols = ['HAS_DEATH', 'HAS_ER_VISIT', 'HAS_HOSPITALIZATION', 
                       'HAS_LIFE_THREAT', 'HAS_DISABILITY']
        for col in outcome_cols:
            if col in features_df.columns:
                features_df[col] = features_df[col].astype(int)
            else:
                features_df[col] = 0
        
        # Age group encoding
        age_groups = ['Infant (0-2)', 'Child (2-12)', 'Adolescent (12-18)', 
                     'Adult (18-65)', 'Elderly (65+)', 'Unknown']
        for age in age_groups:
            features_df[f'age_{age.replace(" ", "_").replace("(", "").replace(")", "").replace("-", "_")}'] = (
                features_df['AGE_GROUP'] == age
            ).astype(int)
        
        # Severity modifier features
        for severity in ['mild', 'moderate', 'severe', 'critical']:
            pattern_count = 0
            if severity in self.severity_patterns:
                for pattern in self.severity_patterns[severity]:
                    pattern_count += features_df['ENTITY_TEXT'].str.count(pattern).fillna(0)
            features_df[f'{severity}_indicators'] = pattern_count
        
        return features_df
    
    def train_ml_classifier(self, df: pd.DataFrame, target_column: str = 'severity_label'):
        """
        Train ML classifier for severity prediction.
        
        Args:
            df: Training dataframe with features and labels
            target_column: Column containing severity labels
        """
        logger.info("Training ML classifier for severity prediction...")
        
        # Extract features
        features_df = self.extract_severity_features(df)
        
        # Select feature columns
        feature_cols = [col for col in features_df.columns if col.startswith(
            ('text_', 'word_', 'HAS_', 'age_', 'mild_', 'moderate_', 'severe_', 'critical_')
        )]
        
        X = features_df[feature_cols].fillna(0)
        y = features_df[target_column]
        
        # Encode labels
        y_encoded = self.label_encoder.fit_transform(y)
        
        # Split data
        X_train, X_test, y_train, y_test = train_test_split(
            X, y_encoded, test_size=0.2, random_state=42, stratify=y_encoded
        )
        
        # Train Random Forest classifier
        self.ml_classifier = RandomForestClassifier(
            n_estimators=100,
            max_depth=10,
            random_state=42,
            class_weight='balanced'
        )
        
        self.ml_classifier.fit(X_train, y_train)
        
        # Evaluate
        y_pred = self.ml_classifier.predict(X_test)
        
        logger.info("ML Classifier Performance:")
        logger.info(f"Classification Report:\n{classification_report(y_test, y_pred)}")
        
        # Feature importance
        feature_importance = pd.DataFrame({
            'feature': feature_cols,
            'importance': self.ml_classifier.feature_importances_
        }).sort_values('importance', ascending=False)
        
        logger.info("Top 10 Most Important Features:")
        logger.info(feature_importance.head(10).to_string())
        
        return self.ml_classifier
    
    def classify_severity_ml(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Classify severity using trained ML model.
        
        Args:
            df: DataFrame with ADE data
            
        Returns:
            DataFrame with ML predictions
        """
        if self.ml_classifier is None:
            logger.error("ML classifier not trained. Call train_ml_classifier first.")
            return df
        
        # Extract features
        features_df = self.extract_severity_features(df)
        
        # Select feature columns
        feature_cols = [col for col in features_df.columns if col.startswith(
            ('text_', 'word_', 'HAS_', 'age_', 'mild_', 'moderate_', 'severe_', 'critical_')
        )]
        
        X = features_df[feature_cols].fillna(0)
        
        # Predict
        predictions = self.ml_classifier.predict(X)
        probabilities = self.ml_classifier.predict_proba(X)
        
        # Decode predictions
        predicted_labels = self.label_encoder.inverse_transform(predictions)
        max_probabilities = np.max(probabilities, axis=1)
        
        # Add predictions to dataframe
        result_df = df.copy()
        result_df['ml_severity'] = predicted_labels
        result_df['ml_confidence'] = max_probabilities
        result_df['ml_method'] = 'machine_learning'
        
        return result_df
    
    def classify_severity_hybrid(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Classify severity using hybrid approach (rules + ML + BioBERT).
        
        Args:
            df: DataFrame with ADE data
            
        Returns:
            DataFrame with hybrid predictions
        """
        logger.info("Performing hybrid severity classification...")
        
        result_df = df.copy()
        
        # Initialize result columns
        result_df['final_severity'] = 'mild'
        result_df['final_confidence'] = 0.0
        result_df['rule_severity'] = 'mild'
        result_df['rule_confidence'] = 0.0
        
        # Apply rule-based classification
        for idx, row in result_df.iterrows():
            text = row.get('ENTITY_TEXT', '')
            age_group = row.get('AGE_GROUP', 'Adult (18-65)')
            severity_score = row.get('SEVERITY_SCORE', 0)
            
            rule_result = self.classify_severity_rules(text, age_group, severity_score)
            result_df.at[idx, 'rule_severity'] = rule_result['predicted_severity']
            result_df.at[idx, 'rule_confidence'] = rule_result['confidence']
        
        # Apply ML classification if available
        if self.ml_classifier is not None:
            ml_df = self.classify_severity_ml(result_df)
            result_df['ml_severity'] = ml_df['ml_severity']
            result_df['ml_confidence'] = ml_df['ml_confidence']
        else:
            result_df['ml_severity'] = result_df['rule_severity']
            result_df['ml_confidence'] = result_df['rule_confidence']
        
        # Combine predictions (weighted average)
        for idx, row in result_df.iterrows():
            rule_sev = row['rule_severity']
            rule_conf = row['rule_confidence']
            ml_sev = row['ml_severity']
            ml_conf = row['ml_confidence']
            
            # Simple voting mechanism
            if rule_sev == ml_sev:
                final_severity = rule_sev
                final_confidence = (rule_conf + ml_conf) / 2
            else:
                # Choose the one with higher confidence
                if rule_conf > ml_conf:
                    final_severity = rule_sev
                    final_confidence = rule_conf
                else:
                    final_severity = ml_sev
                    final_confidence = ml_conf
            
            result_df.at[idx, 'final_severity'] = final_severity
            result_df.at[idx, 'final_confidence'] = final_confidence
        
        logger.info("Hybrid classification complete")
        logger.info(f"Severity distribution: {result_df['final_severity'].value_counts().to_dict()}")
        
        return result_df
    
    def save_model(self, filepath: str):
        """Save trained models to file."""
        if self.ml_classifier is not None:
            model_data = {
                'ml_classifier': self.ml_classifier,
                'label_encoder': self.label_encoder,
                'severity_rules': self.severity_rules
            }
            joblib.dump(model_data, filepath)
            logger.info(f"Model saved to {filepath}")
    
    def load_model(self, filepath: str):
        """Load trained models from file."""
        try:
            model_data = joblib.load(filepath)
            self.ml_classifier = model_data['ml_classifier']
            self.label_encoder = model_data['label_encoder']
            self.severity_rules = model_data['severity_rules']
            logger.info(f"Model loaded from {filepath}")
        except Exception as e:
            logger.error(f"Failed to load model: {e}")


def main():
    """
    Main function to demonstrate severity classification.
    """
    # Create sample data
    sample_data = {
        'ENTITY_TEXT': [
            'severe headache',
            'mild injection site pain',
            'cardiac arrest',
            'slight fever',
            'anaphylactic shock',
            'moderate nausea'
        ],
        'AGE_GROUP': ['Adult (18-65)', 'Adult (18-65)', 'Elderly (65+)', 
                     'Child (2-12)', 'Adult (18-65)', 'Adult (18-65)'],
        'SEVERITY_SCORE': [1, 0, 5, 0, 5, 1],
        'HAS_DEATH': [False, False, True, False, True, False],
        'HAS_ER_VISIT': [True, False, True, False, True, False],
        'HAS_HOSPITALIZATION': [False, False, True, False, True, False]
    }
    
    df = pd.DataFrame(sample_data)
    
    # Initialize classifier
    classifier = SeverityClassifier()
    
    # Test rule-based classification
    print("Rule-based Classification Results:")
    for idx, row in df.iterrows():
        result = classifier.classify_severity_rules(
            row['ENTITY_TEXT'], 
            row['AGE_GROUP'], 
            row['SEVERITY_SCORE']
        )
        print(f"'{row['ENTITY_TEXT']}' -> {result['predicted_severity']} "
              f"(confidence: {result['confidence']:.2f})")


if __name__ == "__main__":
    main()
