"""
ADEGuard Streamlit Application

Interactive web interface for ADE detection, clustering, and severity classification
with token-level highlights and explainability visualizations.
"""

import streamlit as st
import pandas as pd
import numpy as np
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import re
from pathlib import Path
import sys

# Add src directory to path
sys.path.append(str(Path(__file__).parent / "src"))

from preprocessing.data_loader import VAERSDataLoader
from labeling.ner_extractor import ADEDrugNER
from training.clustering import ModifierAwareClusterer
from training.severity_classifier import SeverityClassifier

# Page configuration
st.set_page_config(
    page_title="ADEGuard - AI-Powered ADE Detection System",
    page_icon="🏥",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS for better styling
st.markdown("""
<style>
    .main-header {
        font-size: 3rem;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 2rem;
    }
    .section-header {
        font-size: 1.5rem;
        color: #2c3e50;
        border-bottom: 2px solid #3498db;
        padding-bottom: 0.5rem;
        margin: 1rem 0;
    }
    .ade-highlight {
        background-color: #ffeb3b;
        padding: 2px 4px;
        border-radius: 3px;
        font-weight: bold;
    }
    .drug-highlight {
        background-color: #4caf50;
        color: white;
        padding: 2px 4px;
        border-radius: 3px;
        font-weight: bold;
    }
    .severity-critical {
        color: #d32f2f;
        font-weight: bold;
    }
    .severity-severe {
        color: #f57c00;
        font-weight: bold;
    }
    .severity-moderate {
        color: #fbc02d;
        font-weight: bold;
    }
    .severity-mild {
        color: #388e3c;
        font-weight: bold;
    }
</style>
""", unsafe_allow_html=True)


@st.cache_data
def load_sample_data():
    """Load and cache sample VAERS data."""
    try:
        loader = VAERSDataLoader()
        merged_data = loader.merge_datasets()
        sample_data = loader.get_sample_data(n_samples=200)
        return sample_data
    except Exception as e:
        st.error(f"Error loading data: {e}")
        return pd.DataFrame()


@st.cache_resource
def initialize_models():
    """Initialize and cache NLP models."""
    try:
        ner_model = ADEDrugNER()
        clusterer = ModifierAwareClusterer()
        classifier = SeverityClassifier()
        return ner_model, clusterer, classifier
    except Exception as e:
        st.error(f"Error initializing models: {e}")
        return None, None, None


def highlight_entities(text, entities):
    """Create highlighted text with entity annotations."""
    if not entities:
        return text
    
    # Sort entities by start position (reverse order for replacement)
    sorted_entities = sorted(entities, key=lambda x: x.start, reverse=True)
    
    highlighted_text = text
    for entity in sorted_entities:
        start, end = entity.start, entity.end
        entity_text = entity.text
        
        if entity.label == 'ADE':
            replacement = f'<span class="ade-highlight" title="ADE: {entity.confidence:.2f}">{entity_text}</span>'
        elif entity.label == 'DRUG':
            replacement = f'<span class="drug-highlight" title="DRUG: {entity.confidence:.2f}">{entity_text}</span>'
        else:
            replacement = entity_text
        
        highlighted_text = highlighted_text[:start] + replacement + highlighted_text[end:]
    
    return highlighted_text


def create_severity_badge(severity, confidence):
    """Create a colored severity badge."""
    severity_colors = {
        'critical': '#d32f2f',
        'severe': '#f57c00',
        'moderate': '#fbc02d',
        'mild': '#388e3c'
    }
    
    color = severity_colors.get(severity.lower(), '#757575')
    return f'<span style="background-color: {color}; color: white; padding: 4px 8px; border-radius: 12px; font-size: 0.8rem; font-weight: bold;">{severity.upper()} ({confidence:.2f})</span>'


def main():
    """Main Streamlit application."""
    
    # Header
    st.markdown('<h1 class="main-header">🏥 ADEGuard</h1>', unsafe_allow_html=True)
    st.markdown('<p style="text-align: center; font-size: 1.2rem; color: #7f8c8d;">AI-Powered Adverse Drug Event Detection & Analysis System</p>', unsafe_allow_html=True)
    
    # Sidebar
    st.sidebar.title("Navigation")
    page = st.sidebar.selectbox(
        "Choose a page:",
        ["🏠 Home", "📊 Data Overview", "🔍 ADE Detection", "🎯 Clustering Analysis", "⚠️ Severity Classification", "📈 Analytics Dashboard"]
    )
    
    # Initialize models
    with st.spinner("Loading models..."):
        ner_model, clusterer, classifier = initialize_models()
    
    if ner_model is None:
        st.error("Failed to initialize models. Please check your setup.")
        return
    
    # Load data
    with st.spinner("Loading data..."):
        data = load_sample_data()
    
    if data.empty:
        st.error("No data available. Please check your data files.")
        return
    
    # Page routing
    if page == "🏠 Home":
        show_home_page()
    elif page == "📊 Data Overview":
        show_data_overview(data)
    elif page == "🔍 ADE Detection":
        show_ade_detection(data, ner_model)
    elif page == "🎯 Clustering Analysis":
        show_clustering_analysis(data, ner_model, clusterer)
    elif page == "⚠️ Severity Classification":
        show_severity_classification(data, ner_model, classifier)
    elif page == "📈 Analytics Dashboard":
        show_analytics_dashboard(data, ner_model, clusterer, classifier)


def show_home_page():
    """Display the home page."""
    st.markdown('<div class="section-header">Welcome to ADEGuard</div>', unsafe_allow_html=True)
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        st.markdown("""
        ### 🎯 Key Features
        - **NER Extraction**: BioBERT-powered ADE and DRUG entity detection
        - **Smart Clustering**: Modifier-aware and age-specific grouping
        - **Severity Classification**: Rule-based and ML severity assessment
        - **Interactive Visualizations**: Real-time analysis and insights
        """)
    
    with col2:
        st.markdown("""
        ### 🏥 Use Cases
        - **Hospitals**: Real-time ADE monitoring
        - **Regulators**: Safety signal detection
        - **Pharma**: Drug safety analysis
        - **Researchers**: Clinical data insights
        """)
    
    with col3:
        st.markdown("""
        ### 🔧 Technology Stack
        - **BioBERT**: Biomedical NER and classification
        - **HDBSCAN**: Density-based clustering
        - **Sentence-BERT**: Semantic embeddings
        - **Streamlit**: Interactive web interface
        """)
    
    st.markdown("---")
    
    # Quick stats
    st.markdown('<div class="section-header">System Overview</div>', unsafe_allow_html=True)
    
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric("Data Sources", "VAERS Database", "FDA Approved")
    with col2:
        st.metric("NLP Models", "BioBERT + Rules", "High Accuracy")
    with col3:
        st.metric("Age Groups", "5 Categories", "Age-Aware")
    with col4:
        st.metric("Severity Levels", "4 Levels", "Clinical Guidelines")


def show_data_overview(data):
    """Display data overview and statistics."""
    st.markdown('<div class="section-header">📊 Data Overview</div>', unsafe_allow_html=True)
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.subheader("Dataset Statistics")
        st.metric("Total Records", len(data))
        st.metric("Unique Patients", data['VAERS_ID'].nunique())
        st.metric("Date Range", f"{data['RECVDATE'].min()} - {data['RECVDATE'].max()}")
    
    with col2:
        st.subheader("Age Distribution")
        age_counts = data['AGE_GROUP'].value_counts()
        fig = px.pie(values=age_counts.values, names=age_counts.index, title="Age Group Distribution")
        st.plotly_chart(fig, use_container_width=True)
    
    # Severity distribution
    col1, col2 = st.columns(2)
    
    with col1:
        st.subheader("Severity Score Distribution")
        severity_counts = data['SEVERITY_SCORE'].value_counts().sort_index()
        fig = px.bar(x=severity_counts.index, y=severity_counts.values, 
                    title="Clinical Severity Scores", labels={'x': 'Severity Score', 'y': 'Count'})
        st.plotly_chart(fig, use_container_width=True)
    
    with col2:
        st.subheader("Clinical Outcomes")
        outcomes = ['HAS_DEATH', 'HAS_ER_VISIT', 'HAS_HOSPITALIZATION', 'HAS_LIFE_THREAT', 'HAS_DISABILITY']
        outcome_counts = []
        for outcome in outcomes:
            if outcome in data.columns:
                outcome_counts.append(data[outcome].sum())
            else:
                outcome_counts.append(0)
        
        fig = px.bar(x=[o.replace('HAS_', '') for o in outcomes], y=outcome_counts,
                    title="Clinical Outcomes", labels={'x': 'Outcome Type', 'y': 'Count'})
        st.plotly_chart(fig, use_container_width=True)
    
    # Sample data
    st.subheader("Sample Records")
    display_cols = ['VAERS_ID', 'AGE_GROUP', 'SYMPTOM_TEXT_CLEAN', 'SEVERITY_SCORE']
    available_cols = [col for col in display_cols if col in data.columns]
    st.dataframe(data[available_cols].head(10), use_container_width=True)


def show_ade_detection(data, ner_model):
    """Display ADE detection interface."""
    st.markdown('<div class="section-header">🔍 ADE Detection</div>', unsafe_allow_html=True)
    
    # Text input for custom analysis
    st.subheader("Analyze Custom Text")
    custom_text = st.text_area(
        "Enter symptom text for analysis:",
        value="Patient received Pfizer COVID-19 vaccine and experienced severe headache, injection site pain, and mild fever.",
        height=100
    )
    
    if st.button("Analyze Text"):
        if custom_text:
            with st.spinner("Extracting entities..."):
                entities = ner_model.extract_entities(custom_text)
            
            st.subheader("Analysis Results")
            
            # Highlighted text
            highlighted = highlight_entities(custom_text, entities)
            st.markdown(f"**Highlighted Text:** {highlighted}", unsafe_allow_html=True)
            
            # Entity table
            if entities:
                entity_data = []
                for entity in entities:
                    entity_data.append({
                        'Entity': entity.text,
                        'Type': entity.label,
                        'Confidence': f"{entity.confidence:.2f}",
                        'Position': f"{entity.start}-{entity.end}"
                    })
                
                st.subheader("Detected Entities")
                st.dataframe(pd.DataFrame(entity_data), use_container_width=True)
            else:
                st.info("No entities detected in the text.")
    
    # Batch analysis
    st.subheader("Batch Analysis on Sample Data")
    
    if st.button("Run Batch Analysis"):
        with st.spinner("Processing sample data..."):
            # Process a subset of data
            sample_subset = data.head(20)
            entities_df = ner_model.process_dataframe(sample_subset)
        
        if not entities_df.empty:
            st.success(f"Extracted {len(entities_df)} entities from {len(sample_subset)} records")
            
            # Entity type distribution
            col1, col2 = st.columns(2)
            
            with col1:
                entity_counts = entities_df['ENTITY_LABEL'].value_counts()
                fig = px.pie(values=entity_counts.values, names=entity_counts.index, 
                           title="Entity Type Distribution")
                st.plotly_chart(fig, use_container_width=True)
            
            with col2:
                # Confidence distribution
                fig = px.histogram(entities_df, x='ENTITY_CONFIDENCE', nbins=20,
                                 title="Entity Confidence Distribution")
                st.plotly_chart(fig, use_container_width=True)
            
            # Sample entities
            st.subheader("Sample Extracted Entities")
            display_cols = ['ENTITY_TEXT', 'ENTITY_LABEL', 'ENTITY_CONFIDENCE', 'AGE_GROUP']
            available_cols = [col for col in display_cols if col in entities_df.columns]
            st.dataframe(entities_df[available_cols].head(20), use_container_width=True)


def show_clustering_analysis(data, ner_model, clusterer):
    """Display clustering analysis interface."""
    st.markdown('<div class="section-header">🎯 Clustering Analysis</div>', unsafe_allow_html=True)
    
    st.info("This feature performs modifier-aware and age-specific clustering of ADE symptoms.")
    
    # Clustering parameters
    col1, col2 = st.columns(2)
    
    with col1:
        min_cluster_size = st.slider("Minimum Cluster Size", 2, 20, 5)
    with col2:
        min_samples = st.slider("Minimum Samples", 1, 10, 3)
    
    if st.button("Perform Clustering Analysis"):
        with st.spinner("Extracting entities and clustering..."):
            # Extract entities first
            sample_subset = data.head(50)  # Use smaller subset for demo
            entities_df = ner_model.process_dataframe(sample_subset)
            
            if entities_df.empty:
                st.error("No entities found for clustering.")
                return
            
            # Prepare clustering data
            clustering_df = clusterer.prepare_clustering_data(entities_df)
            
            if clustering_df.empty:
                st.error("No ADE entities found for clustering.")
                return
            
            # Perform clustering
            clustered_df = clusterer.perform_clustering(
                clustering_df, 
                min_cluster_size=min_cluster_size,
                min_samples=min_samples
            )
            
            # Analyze results
            analysis = clusterer.analyze_clusters(clustered_df)
            
            # Create visualizations
            figures = clusterer.create_cluster_visualizations(clustered_df)
        
        # Display results
        st.success("Clustering analysis complete!")
        
        # Summary statistics
        col1, col2, col3 = st.columns(3)
        
        with col1:
            st.metric("Total Entities", analysis['total_entities'])
        with col2:
            st.metric("Clusters Found", analysis['total_clusters'])
        with col3:
            st.metric("Noise Entities", analysis['noise_entities'])
        
        # Visualizations
        if figures:
            for title, fig in figures.items():
                st.subheader(title.replace('_', ' ').title())
                st.plotly_chart(fig, use_container_width=True)
        
        # Cluster summaries
        st.subheader("Cluster Summaries")
        for summary in analysis['cluster_summaries']:
            with st.expander(f"Cluster: {summary['cluster_label']} ({summary['size']} entities)"):
                col1, col2 = st.columns(2)
                
                with col1:
                    st.write("**Age Groups:**")
                    for age, count in summary['age_groups'].items():
                        st.write(f"- {age}: {count}")
                    
                    st.write("**Severities:**")
                    for sev, count in summary['severities'].items():
                        st.write(f"- {sev}: {count}")
                
                with col2:
                    st.write("**Common Terms:**")
                    for term, count in list(summary['common_terms'].items())[:5]:
                        st.write(f"- {term}: {count}")
                    
                    st.write("**Sample Texts:**")
                    for text in summary['sample_texts']:
                        st.write(f"- {text}")


def show_severity_classification(data, ner_model, classifier):
    """Display severity classification interface."""
    st.markdown('<div class="section-header">⚠️ Severity Classification</div>', unsafe_allow_html=True)
    
    # Custom text classification
    st.subheader("Classify Custom Text")
    
    col1, col2 = st.columns(2)
    
    with col1:
        text_input = st.text_area(
            "Enter ADE text:",
            value="Patient experienced severe chest pain and was hospitalized",
            height=100
        )
        age_group = st.selectbox(
            "Age Group:",
            ['Infant (0-2)', 'Child (2-12)', 'Adolescent (12-18)', 'Adult (18-65)', 'Elderly (65+)']
        )
    
    with col2:
        severity_score = st.slider("Clinical Severity Score", 0, 5, 1)
        
        if st.button("Classify Severity"):
            result = classifier.classify_severity_rules(text_input, age_group, severity_score)
            
            st.subheader("Classification Result")
            severity_badge = create_severity_badge(result['predicted_severity'], result['confidence'])
            st.markdown(f"**Predicted Severity:** {severity_badge}", unsafe_allow_html=True)
            
            st.write("**Severity Scores:**")
            for sev, score in result['severity_scores'].items():
                st.write(f"- {sev.title()}: {score}")
    
    # Batch classification
    st.subheader("Batch Severity Classification")
    
    if st.button("Run Batch Classification"):
        with st.spinner("Processing sample data..."):
            # Extract entities first
            sample_subset = data.head(30)
            entities_df = ner_model.process_dataframe(sample_subset)
            
            if entities_df.empty:
                st.error("No entities found for classification.")
                return
            
            # Filter ADE entities
            ade_df = entities_df[entities_df['ENTITY_LABEL'] == 'ADE']
            
            if ade_df.empty:
                st.error("No ADE entities found for classification.")
                return
            
            # Classify severity
            classified_df = classifier.classify_severity_hybrid(ade_df)
        
        st.success(f"Classified {len(classified_df)} ADE entities")
        
        # Severity distribution
        col1, col2 = st.columns(2)
        
        with col1:
            severity_counts = classified_df['final_severity'].value_counts()
            fig = px.pie(values=severity_counts.values, names=severity_counts.index,
                        title="Severity Distribution")
            st.plotly_chart(fig, use_container_width=True)
        
        with col2:
            # Confidence distribution
            fig = px.histogram(classified_df, x='final_confidence', nbins=20,
                             title="Classification Confidence")
            st.plotly_chart(fig, use_container_width=True)
        
        # Sample results
        st.subheader("Sample Classification Results")
        display_cols = ['ENTITY_TEXT', 'final_severity', 'final_confidence', 'AGE_GROUP']
        available_cols = [col for col in display_cols if col in classified_df.columns]
        
        # Add severity badges to display
        display_df = classified_df[available_cols].head(15).copy()
        st.dataframe(display_df, use_container_width=True)


def show_analytics_dashboard(data, ner_model, clusterer, classifier):
    """Display comprehensive analytics dashboard."""
    st.markdown('<div class="section-header">📈 Analytics Dashboard</div>', unsafe_allow_html=True)
    
    st.info("Comprehensive analysis combining all ADEGuard features")
    
    if st.button("Generate Full Analysis Report"):
        with st.spinner("Generating comprehensive analysis..."):
            # Process sample data through full pipeline
            sample_data = data.head(40)
            
            # Step 1: Entity extraction
            entities_df = ner_model.process_dataframe(sample_data)
            
            # Step 2: Severity classification
            if not entities_df.empty:
                ade_df = entities_df[entities_df['ENTITY_LABEL'] == 'ADE']
                if not ade_df.empty:
                    classified_df = classifier.classify_severity_hybrid(ade_df)
                else:
                    classified_df = pd.DataFrame()
            else:
                classified_df = pd.DataFrame()
            
            # Step 3: Clustering
            if not entities_df.empty:
                clustering_df = clusterer.prepare_clustering_data(entities_df)
                if not clustering_df.empty:
                    clustered_df = clusterer.perform_clustering(clustering_df, min_cluster_size=3)
                else:
                    clustered_df = pd.DataFrame()
            else:
                clustered_df = pd.DataFrame()
        
        # Display comprehensive results
        st.success("Analysis complete!")
        
        # Key metrics
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            st.metric("Records Processed", len(sample_data))
        with col2:
            st.metric("Entities Extracted", len(entities_df) if not entities_df.empty else 0)
        with col3:
            st.metric("ADE Entities", len(classified_df) if not classified_df.empty else 0)
        with col4:
            st.metric("Clusters Found", 
                     len(clustered_df['cluster_label'].unique()) if not clustered_df.empty else 0)
        
        # Combined visualizations
        if not classified_df.empty and not clustered_df.empty:
            # Create comprehensive dashboard
            fig = make_subplots(
                rows=2, cols=2,
                subplot_titles=('Entity Types', 'Severity Distribution', 
                              'Age Group Analysis', 'Cluster Overview'),
                specs=[[{"type": "pie"}, {"type": "pie"}],
                       [{"type": "bar"}, {"type": "bar"}]]
            )
            
            # Entity types
            entity_counts = entities_df['ENTITY_LABEL'].value_counts()
            fig.add_trace(
                go.Pie(labels=entity_counts.index, values=entity_counts.values, name="Entities"),
                row=1, col=1
            )
            
            # Severity distribution
            severity_counts = classified_df['final_severity'].value_counts()
            fig.add_trace(
                go.Pie(labels=severity_counts.index, values=severity_counts.values, name="Severity"),
                row=1, col=2
            )
            
            # Age group analysis
            age_severity = classified_df.groupby(['AGE_GROUP', 'final_severity']).size().reset_index(name='count')
            for severity in age_severity['final_severity'].unique():
                severity_data = age_severity[age_severity['final_severity'] == severity]
                fig.add_trace(
                    go.Bar(x=severity_data['AGE_GROUP'], y=severity_data['count'], 
                          name=severity, legendgroup="age"),
                    row=2, col=1
                )
            
            # Cluster overview
            cluster_counts = clustered_df['cluster_label'].value_counts().head(10)
            fig.add_trace(
                go.Bar(x=cluster_counts.index, y=cluster_counts.values, name="Clusters"),
                row=2, col=2
            )
            
            fig.update_layout(height=800, title_text="ADEGuard Comprehensive Analysis Dashboard")
            st.plotly_chart(fig, use_container_width=True)
        
        # Detailed tables
        if not classified_df.empty:
            st.subheader("Detailed Results")
            
            # High-severity ADEs
            high_severity = classified_df[classified_df['final_severity'].isin(['severe', 'critical'])]
            if not high_severity.empty:
                st.write("**High-Severity ADEs:**")
                display_cols = ['ENTITY_TEXT', 'final_severity', 'final_confidence', 'AGE_GROUP']
                available_cols = [col for col in display_cols if col in high_severity.columns]
                st.dataframe(high_severity[available_cols], use_container_width=True)


if __name__ == "__main__":
    main()
