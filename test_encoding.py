"""
Test script to check encoding issues with VAERS data files.
"""

import sys
from pathlib import Path
import pandas as pd

# Add src directory to path
sys.path.append(str(Path(__file__).parent / "src"))

from preprocessing.data_loader import VAERSDataLoader


def test_encoding():
    """Test the encoding fix for VAERS data files."""
    print("🔍 Testing VAERS Data Loading with Encoding Fix")
    print("=" * 50)
    
    # Check if data files exist
    data_dir = Path("data/raw")
    required_files = ["2025VAERSDATA.csv", "2025VAERSSYMPTOMS.csv", "2025VAERSVAX.csv"]
    
    print("📁 Checking for data files...")
    missing_files = []
    for file_name in required_files:
        file_path = data_dir / file_name
        if file_path.exists():
            print(f"   ✅ Found: {file_name}")
        else:
            print(f"   ❌ Missing: {file_name}")
            missing_files.append(file_name)
    
    if missing_files:
        print(f"\n⚠️ Missing files: {', '.join(missing_files)}")
        print("Please download VAERS data files and place them in data/raw/")
        print("Visit: https://vaers.hhs.gov/data/datasets.html")
        return False
    
    # Test loading with encoding fix
    print("\n🔄 Testing data loading with encoding detection...")
    try:
        loader = VAERSDataLoader()
        
        # Test individual file loading
        for file_name in required_files:
            file_path = data_dir / file_name
            print(f"\n📄 Loading {file_name}...")
            
            try:
                df = loader._load_csv_with_encoding(file_path)
                print(f"   ✅ Successfully loaded {len(df)} records")
                print(f"   📊 Columns: {len(df.columns)}")
                print(f"   🔤 Sample columns: {list(df.columns[:5])}")
                
                # Check for any encoding issues in text columns
                text_columns = df.select_dtypes(include=['object']).columns
                if len(text_columns) > 0:
                    sample_col = text_columns[0]
                    sample_text = str(df[sample_col].iloc[0]) if len(df) > 0 else "No data"
                    print(f"   📝 Sample text from {sample_col}: {sample_text[:100]}...")
                
            except Exception as e:
                print(f"   ❌ Failed to load {file_name}: {e}")
                return False
        
        # Test full merge
        print(f"\n🔗 Testing full data merge...")
        merged_data = loader.merge_datasets()
        print(f"   ✅ Successfully merged data: {len(merged_data)} records")
        
        # Test sample extraction
        sample_data = loader.get_sample_data(n_samples=10)
        print(f"   ✅ Sample data extracted: {len(sample_data)} records")
        
        # Show sample of cleaned text
        if 'SYMPTOM_TEXT_CLEAN' in sample_data.columns:
            print(f"\n📝 Sample cleaned symptom texts:")
            for i, text in enumerate(sample_data['SYMPTOM_TEXT_CLEAN'].head(3)):
                print(f"   {i+1}. {text[:150]}...")
        
        print(f"\n🎉 All tests passed! Data loading works correctly.")
        return True
        
    except Exception as e:
        print(f"\n❌ Error during testing: {e}")
        print("This might indicate a more serious issue with the data files.")
        return False


def create_test_files():
    """Create test CSV files with encoding issues for testing."""
    print("\n🧪 Creating test files with different encodings...")
    
    test_dir = Path("data/test")
    test_dir.mkdir(parents=True, exist_ok=True)
    
    # Sample data with special characters
    test_data = {
        'VAERS_ID': [1, 2, 3],
        'TEXT': [
            'Patient experienced café au lait spots',
            'Señor reported naïve symptoms',
            'Résumé of adverse events'
        ]
    }
    
    df = pd.DataFrame(test_data)
    
    # Save with different encodings
    encodings = ['utf-8', 'latin-1', 'cp1252']
    
    for encoding in encodings:
        try:
            file_path = test_dir / f"test_{encoding.replace('-', '_')}.csv"
            df.to_csv(file_path, encoding=encoding, index=False)
            print(f"   ✅ Created test file with {encoding} encoding")
        except Exception as e:
            print(f"   ❌ Failed to create {encoding} file: {e}")
    
    # Test loading these files
    print("\n🔍 Testing encoding detection on test files...")
    loader = VAERSDataLoader()
    
    for encoding in encodings:
        file_path = test_dir / f"test_{encoding.replace('-', '_')}.csv"
        if file_path.exists():
            try:
                test_df = loader._load_csv_with_encoding(file_path)
                print(f"   ✅ Successfully loaded {encoding} test file")
                print(f"      Sample text: {test_df['TEXT'].iloc[0]}")
            except Exception as e:
                print(f"   ❌ Failed to load {encoding} test file: {e}")


if __name__ == "__main__":
    success = test_encoding()
    
    if not success:
        print("\n🔧 Would you like to create test files to verify the encoding fix? (y/n)")
        response = input().lower().strip()
        if response in ['y', 'yes']:
            create_test_files()
    
    print(f"\n{'✅ SUCCESS' if success else '❌ FAILED'}: Encoding test completed")
