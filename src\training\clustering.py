"""
ADEGuard Clustering Module

This module implements modifier-aware and age-specific clustering of ADE symptoms
using HDBSCAN and Sentence-BERT embeddings.
"""

import pandas as pd
import numpy as np
import re
from typing import List, Dict, Tuple, Optional
import logging
from sklearn.preprocessing import StandardScaler
from sklearn.decomposition import PCA
from sklearn.metrics import silhouette_score
import hdbscan
from sentence_transformers import SentenceTransformer
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class ModifierAwareClusterer:
    """
    Clustering system for ADE symptoms with modifier and age awareness.
    
    This class implements:
    - Extraction of severity modifiers (mild, moderate, severe)
    - Age-specific clustering
    - Semantic similarity using Sentence-BERT
    - HDBSCAN clustering with parameter optimization
    """
    
    def __init__(self, embedding_model: str = "all-MiniLM-L6-v2"):
        """
        Initialize the clustering system.
        
        Args:
            embedding_model: Sentence-BERT model name for embeddings
        """
        self.embedding_model_name = embedding_model
        self.sentence_model = None
        self.clusterer = None
        self.scaler = StandardScaler()
        self.pca = PCA(n_components=50)  # Reduce dimensionality for clustering
        
        # Initialize modifier patterns
        self._init_modifier_patterns()
        
        # Load sentence transformer model
        self._load_sentence_model()
    
    def _init_modifier_patterns(self):
        """Initialize patterns for extracting severity modifiers."""
        
        # Severity modifier patterns
        self.severity_patterns = {
            'mild': [
                r'\bmild(?:ly)?\b',
                r'\bslight(?:ly)?\b',
                r'\bminor\b',
                r'\blow(?:-grade)?\b',
                r'\bweak\b'
            ],
            'moderate': [
                r'\bmoderate(?:ly)?\b',
                r'\bmedium\b',
                r'\bmid(?:-grade)?\b',
                r'\bintermediate\b'
            ],
            'severe': [
                r'\bsevere(?:ly)?\b',
                r'\bintense(?:ly)?\b',
                r'\bextreme(?:ly)?\b',
                r'\bacute(?:ly)?\b',
                r'\bhigh(?:-grade)?\b',
                r'\bsignificant(?:ly)?\b',
                r'\bmarked(?:ly)?\b',
                r'\bprofound(?:ly)?\b'
            ]
        }
        
        # Compile regex patterns
        self.severity_regex = {}
        for severity, patterns in self.severity_patterns.items():
            self.severity_regex[severity] = [
                re.compile(pattern, re.IGNORECASE) for pattern in patterns
            ]
        
        # Location modifiers
        self.location_patterns = [
            r'\b(?:left|right)\s+(?:arm|leg|side)\b',
            r'\b(?:upper|lower)\s+(?:arm|leg|back|abdomen)\b',
            r'\b(?:injection\s+site|administration\s+site)\b',
            r'\b(?:chest|abdominal|head|neck)\b'
        ]
        self.location_regex = [re.compile(pattern, re.IGNORECASE) for pattern in self.location_patterns]
        
        # Duration patterns (to exclude from clustering per guidelines)
        self.duration_patterns = [
            r'\bfor\s+\d+\s+(?:days?|hours?|weeks?|months?)\b',
            r'\blasting\s+\d+\s+(?:days?|hours?|weeks?|months?)\b',
            r'\b\d+\s+(?:days?|hours?|weeks?|months?)\s+(?:after|later)\b'
        ]
        self.duration_regex = [re.compile(pattern, re.IGNORECASE) for pattern in self.duration_patterns]
    
    def _load_sentence_model(self):
        """Load Sentence-BERT model for embeddings."""
        try:
            logger.info(f"Loading Sentence-BERT model: {self.embedding_model_name}")
            self.sentence_model = SentenceTransformer(self.embedding_model_name)
            logger.info("Sentence-BERT model loaded successfully")
        except Exception as e:
            logger.error(f"Failed to load Sentence-BERT model: {e}")
            raise
    
    def extract_modifiers(self, text: str) -> Dict[str, any]:
        """
        Extract modifiers from ADE text.
        
        Args:
            text: ADE text to analyze
            
        Returns:
            Dictionary with extracted modifiers
        """
        modifiers = {
            'severity': 'unknown',
            'location': [],
            'has_duration': False,
            'clean_text': text  # Text with duration removed
        }
        
        text_lower = text.lower()
        
        # Extract severity
        for severity, patterns in self.severity_regex.items():
            for pattern in patterns:
                if pattern.search(text):
                    modifiers['severity'] = severity
                    break
            if modifiers['severity'] != 'unknown':
                break
        
        # Extract locations
        for pattern in self.location_regex:
            matches = pattern.findall(text)
            modifiers['location'].extend(matches)
        
        # Check for duration (to mark for exclusion)
        for pattern in self.duration_regex:
            if pattern.search(text):
                modifiers['has_duration'] = True
                # Remove duration from text for clustering
                modifiers['clean_text'] = pattern.sub('', text).strip()
                break
        
        return modifiers
    
    def prepare_clustering_data(self, df: pd.DataFrame, entity_column: str = 'ENTITY_TEXT') -> pd.DataFrame:
        """
        Prepare data for clustering by extracting modifiers and creating features.
        
        Args:
            df: DataFrame with ADE entities
            entity_column: Column containing ADE text
            
        Returns:
            DataFrame prepared for clustering
        """
        logger.info("Preparing data for clustering...")
        
        # Filter for ADE entities only
        ade_df = df[df['ENTITY_LABEL'] == 'ADE'].copy()
        
        if len(ade_df) == 0:
            logger.warning("No ADE entities found for clustering")
            return pd.DataFrame()
        
        # Extract modifiers for each ADE
        modifier_data = []
        for idx, row in ade_df.iterrows():
            text = row[entity_column]
            modifiers = self.extract_modifiers(text)
            
            modifier_record = {
                'original_index': idx,
                'ade_text': text,
                'clean_text': modifiers['clean_text'],
                'severity': modifiers['severity'],
                'location_count': len(modifiers['location']),
                'has_location': len(modifiers['location']) > 0,
                'has_duration': modifiers['has_duration'],
                'age_group': row.get('AGE_GROUP', 'Unknown'),
                'severity_score': row.get('SEVERITY_SCORE', 0)
            }
            
            # Add original row data
            for col in ade_df.columns:
                if col not in modifier_record:
                    modifier_record[col] = row[col]
            
            modifier_data.append(modifier_record)
        
        clustering_df = pd.DataFrame(modifier_data)
        
        # Create embeddings for clean text
        logger.info("Creating embeddings...")
        embeddings = self.sentence_model.encode(
            clustering_df['clean_text'].tolist(),
            show_progress_bar=True
        )
        
        # Add embeddings to dataframe
        for i, embedding in enumerate(embeddings):
            clustering_df[f'emb_{i}'] = embedding
        
        logger.info(f"Prepared {len(clustering_df)} ADE entities for clustering")
        return clustering_df
    
    def perform_clustering(self, df: pd.DataFrame, min_cluster_size: int = 5, 
                          min_samples: int = 3) -> pd.DataFrame:
        """
        Perform HDBSCAN clustering on ADE entities.
        
        Args:
            df: Prepared clustering dataframe
            min_cluster_size: Minimum cluster size for HDBSCAN
            min_samples: Minimum samples for HDBSCAN
            
        Returns:
            DataFrame with cluster assignments
        """
        if len(df) == 0:
            return df
        
        logger.info("Performing clustering...")
        
        # Extract embedding columns
        embedding_cols = [col for col in df.columns if col.startswith('emb_')]
        embeddings = df[embedding_cols].values
        
        # Standardize embeddings
        embeddings_scaled = self.scaler.fit_transform(embeddings)
        
        # Apply PCA for dimensionality reduction
        embeddings_pca = self.pca.fit_transform(embeddings_scaled)
        
        # Perform clustering for each age group separately
        clustered_dfs = []
        
        for age_group in df['age_group'].unique():
            age_mask = df['age_group'] == age_group
            age_df = df[age_mask].copy()
            age_embeddings = embeddings_pca[age_mask]
            
            if len(age_df) < min_cluster_size:
                # Too few samples for this age group
                age_df['cluster'] = -1
                age_df['cluster_label'] = 'noise'
                clustered_dfs.append(age_df)
                continue
            
            # Adjust parameters based on data size
            adjusted_min_cluster_size = min(min_cluster_size, len(age_df) // 3)
            adjusted_min_samples = min(min_samples, adjusted_min_cluster_size)
            
            # Perform HDBSCAN clustering
            clusterer = hdbscan.HDBSCAN(
                min_cluster_size=adjusted_min_cluster_size,
                min_samples=adjusted_min_samples,
                metric='euclidean',
                cluster_selection_method='eom'
            )
            
            cluster_labels = clusterer.fit_predict(age_embeddings)
            
            # Add cluster information
            age_df['cluster'] = cluster_labels
            age_df['cluster_label'] = [
                f"{age_group}_cluster_{label}" if label != -1 else "noise"
                for label in cluster_labels
            ]
            
            # Calculate cluster statistics
            if len(set(cluster_labels)) > 1:
                try:
                    silhouette_avg = silhouette_score(age_embeddings, cluster_labels)
                    age_df['silhouette_score'] = silhouette_avg
                except:
                    age_df['silhouette_score'] = 0.0
            else:
                age_df['silhouette_score'] = 0.0
            
            clustered_dfs.append(age_df)
            
            logger.info(f"Age group {age_group}: {len(set(cluster_labels))} clusters, "
                       f"silhouette score: {age_df['silhouette_score'].iloc[0]:.3f}")
        
        # Combine all age groups
        result_df = pd.concat(clustered_dfs, ignore_index=True)
        
        # Add severity-based sub-clustering within each cluster
        result_df = self._add_severity_subclusters(result_df)
        
        logger.info(f"Clustering complete. Found {len(result_df['cluster_label'].unique())} unique clusters")
        
        return result_df
    
    def _add_severity_subclusters(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add severity-based sub-clustering within main clusters."""
        
        df['severity_subcluster'] = df['cluster_label'].copy()
        
        # For each main cluster, create severity subclusters
        for cluster_label in df['cluster_label'].unique():
            if cluster_label == 'noise':
                continue
            
            cluster_mask = df['cluster_label'] == cluster_label
            cluster_df = df[cluster_mask]
            
            # Group by severity within cluster
            severity_counts = cluster_df['severity'].value_counts()
            
            # Only create subclusters if there are multiple severities
            if len(severity_counts) > 1:
                for severity in severity_counts.index:
                    severity_mask = cluster_mask & (df['severity'] == severity)
                    df.loc[severity_mask, 'severity_subcluster'] = f"{cluster_label}_{severity}"
        
        return df
    
    def analyze_clusters(self, df: pd.DataFrame) -> Dict[str, any]:
        """
        Analyze clustering results and generate insights.
        
        Args:
            df: Clustered dataframe
            
        Returns:
            Dictionary with cluster analysis
        """
        analysis = {
            'total_entities': len(df),
            'total_clusters': len(df[df['cluster'] != -1]['cluster_label'].unique()),
            'noise_entities': len(df[df['cluster'] == -1]),
            'age_group_distribution': df['age_group'].value_counts().to_dict(),
            'severity_distribution': df['severity'].value_counts().to_dict(),
            'cluster_summaries': []
        }
        
        # Analyze each cluster
        for cluster_label in df['cluster_label'].unique():
            if cluster_label == 'noise':
                continue
            
            cluster_df = df[df['cluster_label'] == cluster_label]
            
            # Get most common terms in cluster
            all_text = ' '.join(cluster_df['clean_text'].tolist())
            words = re.findall(r'\b\w+\b', all_text.lower())
            word_freq = pd.Series(words).value_counts().head(10)
            
            cluster_summary = {
                'cluster_label': cluster_label,
                'size': len(cluster_df),
                'age_groups': cluster_df['age_group'].value_counts().to_dict(),
                'severities': cluster_df['severity'].value_counts().to_dict(),
                'common_terms': word_freq.to_dict(),
                'sample_texts': cluster_df['clean_text'].head(3).tolist(),
                'avg_severity_score': cluster_df['severity_score'].mean()
            }
            
            analysis['cluster_summaries'].append(cluster_summary)
        
        return analysis
    
    def create_cluster_visualizations(self, df: pd.DataFrame) -> Dict[str, go.Figure]:
        """
        Create visualizations for clustering results.
        
        Args:
            df: Clustered dataframe
            
        Returns:
            Dictionary of plotly figures
        """
        figures = {}
        
        if len(df) == 0:
            return figures
        
        # 1. Cluster distribution by age group
        cluster_age_counts = df.groupby(['cluster_label', 'age_group']).size().reset_index(name='count')
        
        fig1 = px.bar(
            cluster_age_counts,
            x='cluster_label',
            y='count',
            color='age_group',
            title='Cluster Distribution by Age Group',
            labels={'cluster_label': 'Cluster', 'count': 'Number of ADEs'}
        )
        fig1.update_xaxes(tickangle=45)
        figures['cluster_age_distribution'] = fig1
        
        # 2. Severity distribution within clusters
        severity_counts = df.groupby(['cluster_label', 'severity']).size().reset_index(name='count')
        
        fig2 = px.bar(
            severity_counts,
            x='cluster_label',
            y='count',
            color='severity',
            title='Severity Distribution within Clusters',
            labels={'cluster_label': 'Cluster', 'count': 'Number of ADEs'}
        )
        fig2.update_xaxes(tickangle=45)
        figures['severity_distribution'] = fig2
        
        # 3. 2D visualization of clusters (using first 2 PCA components)
        if hasattr(self, 'pca') and self.pca is not None:
            embedding_cols = [col for col in df.columns if col.startswith('emb_')]
            if embedding_cols:
                embeddings = df[embedding_cols].values
                embeddings_scaled = self.scaler.transform(embeddings)
                embeddings_2d = PCA(n_components=2).fit_transform(embeddings_scaled)
                
                df_viz = df.copy()
                df_viz['pca_1'] = embeddings_2d[:, 0]
                df_viz['pca_2'] = embeddings_2d[:, 1]
                
                fig3 = px.scatter(
                    df_viz,
                    x='pca_1',
                    y='pca_2',
                    color='cluster_label',
                    hover_data=['clean_text', 'severity', 'age_group'],
                    title='2D Visualization of ADE Clusters',
                    labels={'pca_1': 'PCA Component 1', 'pca_2': 'PCA Component 2'}
                )
                figures['cluster_2d_viz'] = fig3
        
        return figures


def main():
    """
    Main function to demonstrate clustering functionality.
    """
    # Create sample data for testing
    sample_data = {
        'ENTITY_TEXT': [
            'severe headache',
            'mild headache',
            'injection site pain',
            'severe injection site pain',
            'fever',
            'high fever',
            'nausea',
            'severe nausea and vomiting',
            'chest pain',
            'severe chest pain'
        ],
        'ENTITY_LABEL': ['ADE'] * 10,
        'AGE_GROUP': ['Adult (18-65)'] * 5 + ['Elderly (65+)'] * 5,
        'SEVERITY_SCORE': [1, 1, 1, 2, 1, 2, 1, 3, 2, 3]
    }
    
    df = pd.DataFrame(sample_data)
    
    # Initialize clusterer
    clusterer = ModifierAwareClusterer()
    
    # Prepare data
    clustering_df = clusterer.prepare_clustering_data(df)
    
    # Perform clustering
    clustered_df = clusterer.perform_clustering(clustering_df, min_cluster_size=2)
    
    # Analyze results
    analysis = clusterer.analyze_clusters(clustered_df)
    
    print("Clustering Analysis:")
    print(f"Total entities: {analysis['total_entities']}")
    print(f"Total clusters: {analysis['total_clusters']}")
    print(f"Noise entities: {analysis['noise_entities']}")
    
    print("\nCluster summaries:")
    for summary in analysis['cluster_summaries']:
        print(f"- {summary['cluster_label']}: {summary['size']} entities")
        print(f"  Common terms: {list(summary['common_terms'].keys())[:5]}")


if __name__ == "__main__":
    main()
