"""
ADEGuard Data Loading Module

This module handles loading and merging VAERS data files following the annotation guidelines.
It processes the three main VAERS files: DATA, SYMPTOMS, and VAX files.
"""

import pandas as pd
import numpy as np
import re
from typing import Dict, List, Tuple, Optional
import logging
from pathlib import Path

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class VAERSDataLoader:
    """
    Loads and processes VAERS data files following annotation guidelines.
    
    This class handles:
    - Loading the three VAERS CSV files
    - Merging them into a unified dataset
    - Basic data cleaning and validation
    - Preparing text for NLP processing
    """
    
    def __init__(self, data_dir: str = "data/raw"):
        """
        Initialize the data loader.
        
        Args:
            data_dir: Directory containing the VAERS CSV files
        """
        self.data_dir = Path(data_dir)
        self.data_df = None
        self.symptoms_df = None
        self.vax_df = None
        self.merged_df = None
        
    def load_raw_data(self) -> Tuple[pd.<PERSON><PERSON>rame, pd.DataFrame, pd.DataFrame]:
        """
        Load the three VAERS CSV files.
        
        Returns:
            Tuple of (data_df, symptoms_df, vax_df)
        """
        logger.info("Loading VAERS data files...")
        
        # Load main data file
        data_file = self.data_dir / "2025VAERSDATA.csv"
        self.data_df = pd.read_csv(data_file, low_memory=False)
        logger.info(f"Loaded {len(self.data_df)} records from DATA file")
        
        # Load symptoms file
        symptoms_file = self.data_dir / "2025VAERSSYMPTOMS.csv"
        self.symptoms_df = pd.read_csv(symptoms_file, low_memory=False)
        logger.info(f"Loaded {len(self.symptoms_df)} records from SYMPTOMS file")
        
        # Load vaccine file
        vax_file = self.data_dir / "2025VAERSVAX.csv"
        self.vax_df = pd.read_csv(vax_file, low_memory=False)
        logger.info(f"Loaded {len(self.vax_df)} records from VAX file")
        
        return self.data_df, self.symptoms_df, self.vax_df
    
    def clean_symptom_text(self, text: str) -> str:
        """
        Clean symptom text following annotation guidelines.
        
        This function:
        - Removes excessive whitespace
        - Handles encoding issues
        - Preserves original casing (as per guidelines)
        - Removes non-printable characters
        
        Args:
            text: Raw symptom text
            
        Returns:
            Cleaned text
        """
        if pd.isna(text) or text == "":
            return ""
            
        # Convert to string if not already
        text = str(text)
        
        # Remove excessive whitespace but preserve single spaces
        text = re.sub(r'\s+', ' ', text)
        
        # Remove non-printable characters except newlines and tabs
        text = re.sub(r'[^\x20-\x7E\n\t]', '', text)
        
        # Strip leading/trailing whitespace
        text = text.strip()
        
        return text
    
    def extract_age_group(self, age_yrs: float) -> str:
        """
        Extract age group for age-specific clustering.
        
        Args:
            age_yrs: Age in years
            
        Returns:
            Age group category
        """
        if pd.isna(age_yrs):
            return "Unknown"
        
        age = float(age_yrs)
        
        if age < 2:
            return "Infant (0-2)"
        elif age < 12:
            return "Child (2-12)"
        elif age < 18:
            return "Adolescent (12-18)"
        elif age < 65:
            return "Adult (18-65)"
        else:
            return "Elderly (65+)"
    
    def merge_datasets(self) -> pd.DataFrame:
        """
        Merge the three VAERS datasets into a unified dataframe.
        
        Returns:
            Merged dataframe with all relevant information
        """
        logger.info("Merging VAERS datasets...")
        
        if self.data_df is None:
            self.load_raw_data()
        
        # Start with the main data file
        merged = self.data_df.copy()
        
        # Add age groups
        merged['AGE_GROUP'] = merged['AGE_YRS'].apply(self.extract_age_group)
        
        # Clean symptom text
        merged['SYMPTOM_TEXT_CLEAN'] = merged['SYMPTOM_TEXT'].apply(self.clean_symptom_text)
        
        # Merge with vaccine information
        # Group vaccines by VAERS_ID to handle multiple vaccines per report
        vax_grouped = self.vax_df.groupby('VAERS_ID').agg({
            'VAX_NAME': lambda x: ' | '.join(x.dropna().astype(str)),
            'VAX_MANU': lambda x: ' | '.join(x.dropna().astype(str)),
            'VAX_TYPE': lambda x: ' | '.join(x.dropna().astype(str))
        }).reset_index()
        
        merged = merged.merge(vax_grouped, on='VAERS_ID', how='left')
        
        # Merge with symptoms information
        # Group symptoms by VAERS_ID to handle multiple symptom entries
        symptoms_cols = [f'SYMPTOM{i}' for i in range(1, 6)]
        symptoms_melted = []
        
        for col in symptoms_cols:
            if col in self.symptoms_df.columns:
                temp_df = self.symptoms_df[['VAERS_ID', col]].dropna()
                temp_df = temp_df[temp_df[col] != '']
                temp_df['SYMPTOM'] = temp_df[col]
                symptoms_melted.append(temp_df[['VAERS_ID', 'SYMPTOM']])
        
        if symptoms_melted:
            all_symptoms = pd.concat(symptoms_melted, ignore_index=True)
            symptoms_grouped = all_symptoms.groupby('VAERS_ID')['SYMPTOM'].apply(
                lambda x: ' | '.join(x.unique())
            ).reset_index()
            symptoms_grouped.columns = ['VAERS_ID', 'CODED_SYMPTOMS']
            
            merged = merged.merge(symptoms_grouped, on='VAERS_ID', how='left')
        
        # Filter out records without symptom text (as per guidelines)
        merged = merged[merged['SYMPTOM_TEXT_CLEAN'].str.len() > 0]
        
        # Add basic severity indicators
        merged['HAS_DEATH'] = merged['DIED'] == 'Y'
        merged['HAS_ER_VISIT'] = merged['ER_VISIT'] == 'Y'
        merged['HAS_HOSPITALIZATION'] = merged['HOSPITAL'] == 'Y'
        merged['HAS_LIFE_THREAT'] = merged['L_THREAT'] == 'Y'
        merged['HAS_DISABILITY'] = merged['DISABLE'] == 'Y'
        
        # Calculate severity score (0-5 based on serious outcomes)
        severity_cols = ['HAS_DEATH', 'HAS_ER_VISIT', 'HAS_HOSPITALIZATION', 
                        'HAS_LIFE_THREAT', 'HAS_DISABILITY']
        merged['SEVERITY_SCORE'] = merged[severity_cols].sum(axis=1)
        
        self.merged_df = merged
        logger.info(f"Merged dataset contains {len(merged)} records")
        
        return merged
    
    def get_sample_data(self, n_samples: int = 100, random_state: int = 42) -> pd.DataFrame:
        """
        Get a sample of the data for annotation and testing.
        
        Args:
            n_samples: Number of samples to return
            random_state: Random seed for reproducibility
            
        Returns:
            Sample dataframe
        """
        if self.merged_df is None:
            self.merge_datasets()
        
        # Stratified sampling by age group and severity
        sample_df = self.merged_df.groupby(['AGE_GROUP', 'SEVERITY_SCORE']).apply(
            lambda x: x.sample(min(len(x), max(1, n_samples // 20)), random_state=random_state)
        ).reset_index(drop=True)
        
        # If we don't have enough samples, fill with random samples
        if len(sample_df) < n_samples:
            remaining = n_samples - len(sample_df)
            additional = self.merged_df[~self.merged_df.index.isin(sample_df.index)].sample(
                min(remaining, len(self.merged_df) - len(sample_df)), 
                random_state=random_state
            )
            sample_df = pd.concat([sample_df, additional], ignore_index=True)
        
        return sample_df.head(n_samples)
    
    def save_processed_data(self, output_path: str = "data/processed/vaers_merged.csv"):
        """
        Save the merged and processed data.
        
        Args:
            output_path: Path to save the processed data
        """
        if self.merged_df is None:
            self.merge_datasets()
        
        output_file = Path(output_path)
        output_file.parent.mkdir(parents=True, exist_ok=True)
        
        self.merged_df.to_csv(output_file, index=False)
        logger.info(f"Saved processed data to {output_file}")


def main():
    """
    Main function to demonstrate data loading and processing.
    """
    # Initialize data loader
    loader = VAERSDataLoader()
    
    # Load and merge data
    merged_data = loader.merge_datasets()
    
    # Print basic statistics
    print(f"Total records: {len(merged_data)}")
    print(f"Age group distribution:")
    print(merged_data['AGE_GROUP'].value_counts())
    print(f"\nSeverity score distribution:")
    print(merged_data['SEVERITY_SCORE'].value_counts())
    
    # Get sample data
    sample_data = loader.get_sample_data(n_samples=50)
    print(f"\nSample data shape: {sample_data.shape}")
    
    # Save processed data
    loader.save_processed_data()
    
    return merged_data


if __name__ == "__main__":
    main()
