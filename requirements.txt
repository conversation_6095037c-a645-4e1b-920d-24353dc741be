# ADEGuard Requirements
# Core data processing
pandas>=1.5.0
numpy>=1.21.0

# NLP and ML libraries
transformers>=4.20.0
torch>=1.12.0
sentence-transformers>=2.2.0
scikit-learn>=1.1.0
spacy>=3.4.0

# Clustering
hdbscan>=0.8.28

# Visualization and UI
streamlit>=1.25.0
plotly>=5.10.0

# Utilities
joblib>=1.1.0
pathlib2>=2.3.0

# Optional: For enhanced biomedical NLP
# scispacy>=0.5.0

# Development and testing (optional)
pytest>=7.0.0
jupyter>=1.0.0
