"""
ADEGuard NER Extraction Module

This module implements Named Entity Recognition for ADE and DRUG entities
following the VAERS annotation guidelines using BioBERT and rule-based approaches.
"""

import re
import spacy
import pandas as pd
import numpy as np
from typing import List, Dict, Tuple, Optional, Set
import logging
from transformers import AutoTokenizer, AutoModelForTokenClassification, pipeline
from dataclasses import dataclass

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@dataclass
class EntitySpan:
    """Represents an extracted entity span."""
    text: str
    start: int
    end: int
    label: str
    confidence: float = 0.0


class ADEDrugNER:
    """
    Named Entity Recognition system for ADE and DRUG entities.
    
    This class implements the annotation guidelines for extracting:
    - ADE spans: Adverse drug events, symptoms, diagnoses
    - DRUG spans: Vaccines, medications, drug products
    """
    
    def __init__(self, model_name: str = "dmis-lab/biobert-base-cased-v1.1"):
        """
        Initialize the NER system.
        
        Args:
            model_name: HuggingFace model name for biomedical NER
        """
        self.model_name = model_name
        self.tokenizer = None
        self.model = None
        self.nlp_pipeline = None
        self.spacy_nlp = None
        
        # Load spaCy for basic NLP tasks
        try:
            self.spacy_nlp = spacy.load("en_core_web_sm")
        except OSError:
            logger.warning("spaCy model not found. Install with: python -m spacy download en_core_web_sm")
        
        # Initialize rule-based patterns based on annotation guidelines
        self._init_rule_patterns()
    
    def _init_rule_patterns(self):
        """Initialize rule-based patterns for ADE and DRUG detection."""
        
        # ADE patterns - symptoms and medical conditions
        self.ade_patterns = [
            # Common symptoms with modifiers
            r'\b(?:severe|mild|moderate|acute|chronic|extreme)\s+(?:headache|fever|pain|fatigue|nausea|dizziness)\b',
            r'\b(?:shortness of breath|difficulty breathing|chest pain|abdominal pain)\b',
            r'\b(?:injection site\s+(?:pain|swelling|redness|reaction))\b',
            r'\b(?:allergic reaction|anaphylaxis|hypersensitivity)\b',
            r'\b(?:myocarditis|pericarditis|thrombosis|embolism)\b',
            r'\b(?:Bell\'s palsy|facial paralysis|nerve injury)\b',
            r'\b(?:seizure|convulsion|syncope|fainting)\b',
            r'\b(?:rash|urticaria|hives|erythema|swelling)\b',
            r'\b(?:COVID-19|coronavirus infection)\b',  # When it's an infection, not vaccine name
            
            # Lab findings and diagnoses
            r'\b(?:elevated|increased|decreased)\s+(?:temperature|blood pressure|heart rate)\b',
            r'\b(?:abnormal|irregular)\s+(?:heart rhythm|cardiac function)\b',
            
            # Location-specific symptoms
            r'\b(?:left|right)\s+arm\s+(?:pain|swelling|weakness)\b',
            r'\b(?:upper|lower)\s+(?:respiratory|gastrointestinal)\s+symptoms?\b',
        ]
        
        # DRUG patterns - vaccines and medications
        self.drug_patterns = [
            # Specific vaccine names
            r'\b(?:Pfizer|Moderna|Johnson\s*&?\s*Johnson|J&J)\s*(?:COVID-19\s*)?(?:vaccine)?\b',
            r'\b(?:Shingrix|Zostavax)\b',
            r'\b(?:Fluzone|FluMist|influenza\s+vaccine)\b',
            r'\b(?:Havrix|hepatitis\s+[AB]?\s+vaccine)\b',
            r'\b(?:Pentacel|DTaP|IPV|HIB)\b',
            r'\b(?:Prevnar|pneumococcal\s+vaccine)\b',
            r'\b(?:Rotateq|rotavirus\s+vaccine)\b',
            
            # Generic vaccine terms
            r'\b(?:COVID-19\s+vaccine|coronavirus\s+vaccine)\b',
            r'\b(?:mRNA\s+vaccine|viral\s+vector\s+vaccine)\b',
            r'\b(?:booster\s+shot|booster\s+dose)\b',
            r'\b(?:first\s+dose|second\s+dose|third\s+dose)\b',
            
            # Product codes and lot numbers
            r'\b[A-Z]{2,}\d{3,}[A-Z]*\b',  # Lot number pattern
            r'\bmRNA-\d{4}\b',  # mRNA product codes
        ]
        
        # Compile patterns
        self.ade_regex = [re.compile(pattern, re.IGNORECASE) for pattern in self.ade_patterns]
        self.drug_regex = [re.compile(pattern, re.IGNORECASE) for pattern in self.drug_patterns]
        
        # Exclusion patterns (things to skip per guidelines)
        self.exclusion_patterns = [
            r'\b(?:not feeling well|general malaise)\b',  # Too vague
            r'\b(?:product dose omission|administration error)\b',  # Administrative only
            r'\b(?:history of|past medical history)\b',  # Pre-existing conditions
            r'\b(?:tested negative|negative test)\b',  # Negative results
            r'\b(?:for \d+ days?|lasting \d+ hours?)\b',  # Time duration
        ]
        self.exclusion_regex = [re.compile(pattern, re.IGNORECASE) for pattern in self.exclusion_patterns]
    
    def load_biobert_model(self):
        """Load BioBERT model for NER."""
        try:
            logger.info(f"Loading BioBERT model: {self.model_name}")
            self.tokenizer = AutoTokenizer.from_pretrained(self.model_name)
            self.model = AutoModelForTokenClassification.from_pretrained(self.model_name)
            
            # Create NER pipeline
            self.nlp_pipeline = pipeline(
                "ner",
                model=self.model,
                tokenizer=self.tokenizer,
                aggregation_strategy="simple"
            )
            logger.info("BioBERT model loaded successfully")
        except Exception as e:
            logger.error(f"Failed to load BioBERT model: {e}")
            logger.info("Falling back to rule-based extraction only")
    
    def extract_rule_based_entities(self, text: str) -> List[EntitySpan]:
        """
        Extract entities using rule-based patterns following annotation guidelines.
        
        Args:
            text: Input text to process
            
        Returns:
            List of extracted entity spans
        """
        entities = []
        
        # Check for exclusion patterns first
        for exclusion_pattern in self.exclusion_regex:
            if exclusion_pattern.search(text):
                # Mark regions to exclude (simplified approach)
                pass
        
        # Extract ADE entities
        for pattern in self.ade_regex:
            for match in pattern.finditer(text):
                # Validate the match according to guidelines
                matched_text = match.group().strip()
                
                # Skip if too short or contains only common words
                if len(matched_text) < 3:
                    continue
                
                # Check if it's a valid medical term (basic validation)
                if self._is_valid_ade(matched_text, text, match.start()):
                    entities.append(EntitySpan(
                        text=matched_text,
                        start=match.start(),
                        end=match.end(),
                        label="ADE",
                        confidence=0.8
                    ))
        
        # Extract DRUG entities
        for pattern in self.drug_regex:
            for match in pattern.finditer(text):
                matched_text = match.group().strip()
                
                if len(matched_text) < 2:
                    continue
                
                if self._is_valid_drug(matched_text, text, match.start()):
                    entities.append(EntitySpan(
                        text=matched_text,
                        start=match.start(),
                        end=match.end(),
                        label="DRUG",
                        confidence=0.8
                    ))
        
        # Remove overlapping entities (keep longer ones)
        entities = self._remove_overlaps(entities)
        
        return entities
    
    def _is_valid_ade(self, text: str, full_text: str, start_pos: int) -> bool:
        """
        Validate ADE entity according to annotation guidelines.
        
        Args:
            text: Extracted text
            full_text: Full context
            start_pos: Start position in full text
            
        Returns:
            True if valid ADE entity
        """
        text_lower = text.lower()
        
        # Skip if it's part of a vaccine name
        if "covid-19" in text_lower and "vaccine" in full_text[max(0, start_pos-20):start_pos+len(text)+20].lower():
            return False
        
        # Skip if it's administrative only
        admin_terms = ["dose", "administration", "given", "received"]
        if any(term in text_lower for term in admin_terms) and not any(
            symptom in text_lower for symptom in ["pain", "reaction", "swelling", "redness"]
        ):
            return False
        
        # Skip if it's clearly pre-existing (basic check)
        context = full_text[max(0, start_pos-50):start_pos+len(text)+50].lower()
        if any(term in context for term in ["history of", "past", "previous", "prior to vaccination"]):
            return False
        
        return True
    
    def _is_valid_drug(self, text: str, full_text: str, start_pos: int) -> bool:
        """
        Validate DRUG entity according to annotation guidelines.
        
        Args:
            text: Extracted text
            full_text: Full context
            start_pos: Start position in full text
            
        Returns:
            True if valid DRUG entity
        """
        text_lower = text.lower()
        
        # Skip if it's past medication
        context = full_text[max(0, start_pos-30):start_pos+len(text)+30].lower()
        if any(term in context for term in ["discontinued", "stopped", "past use", "previously"]):
            return False
        
        return True
    
    def _remove_overlaps(self, entities: List[EntitySpan]) -> List[EntitySpan]:
        """
        Remove overlapping entities, keeping the longer ones.
        
        Args:
            entities: List of entity spans
            
        Returns:
            List of non-overlapping entities
        """
        if not entities:
            return entities
        
        # Sort by start position
        entities.sort(key=lambda x: x.start)
        
        filtered = []
        for entity in entities:
            # Check if it overlaps with any entity in filtered list
            overlaps = False
            for existing in filtered:
                if (entity.start < existing.end and entity.end > existing.start):
                    # There's an overlap
                    if len(entity.text) > len(existing.text):
                        # Remove the shorter existing entity
                        filtered.remove(existing)
                        break
                    else:
                        # Skip this entity
                        overlaps = True
                        break
            
            if not overlaps:
                filtered.append(entity)
        
        return filtered
    
    def extract_entities(self, text: str, use_biobert: bool = True) -> List[EntitySpan]:
        """
        Extract ADE and DRUG entities from text.
        
        Args:
            text: Input text
            use_biobert: Whether to use BioBERT model
            
        Returns:
            List of extracted entities
        """
        if not text or len(text.strip()) == 0:
            return []
        
        # Start with rule-based extraction
        entities = self.extract_rule_based_entities(text)
        
        # Add BioBERT extraction if available
        if use_biobert and self.nlp_pipeline is not None:
            try:
                biobert_entities = self._extract_biobert_entities(text)
                entities.extend(biobert_entities)
                
                # Remove duplicates and overlaps
                entities = self._remove_overlaps(entities)
            except Exception as e:
                logger.warning(f"BioBERT extraction failed: {e}")
        
        return entities
    
    def _extract_biobert_entities(self, text: str) -> List[EntitySpan]:
        """Extract entities using BioBERT model."""
        try:
            results = self.nlp_pipeline(text)
            entities = []
            
            for result in results:
                # Map BioBERT labels to our labels
                label = self._map_biobert_label(result['entity_group'])
                if label in ['ADE', 'DRUG']:
                    entities.append(EntitySpan(
                        text=result['word'],
                        start=result['start'],
                        end=result['end'],
                        label=label,
                        confidence=result['score']
                    ))
            
            return entities
        except Exception as e:
            logger.error(f"BioBERT extraction error: {e}")
            return []
    
    def _map_biobert_label(self, biobert_label: str) -> str:
        """Map BioBERT entity labels to our ADE/DRUG labels."""
        # This is a simplified mapping - in practice, you'd need a more sophisticated approach
        medical_terms = ['DISEASE', 'SYMPTOM', 'ADVERSE_EVENT', 'CONDITION']
        drug_terms = ['DRUG', 'MEDICATION', 'VACCINE', 'CHEMICAL']
        
        if any(term in biobert_label.upper() for term in medical_terms):
            return 'ADE'
        elif any(term in biobert_label.upper() for term in drug_terms):
            return 'DRUG'
        else:
            return 'OTHER'
    
    def process_dataframe(self, df: pd.DataFrame, text_column: str = 'SYMPTOM_TEXT_CLEAN') -> pd.DataFrame:
        """
        Process a dataframe and extract entities from text column.
        
        Args:
            df: Input dataframe
            text_column: Column containing text to process
            
        Returns:
            Dataframe with extracted entities
        """
        logger.info(f"Processing {len(df)} records for entity extraction...")
        
        results = []
        for idx, row in df.iterrows():
            text = row[text_column] if text_column in row else ""
            entities = self.extract_entities(text)
            
            # Create a record for each entity
            for entity in entities:
                result = row.to_dict()
                result.update({
                    'ENTITY_TEXT': entity.text,
                    'ENTITY_LABEL': entity.label,
                    'ENTITY_START': entity.start,
                    'ENTITY_END': entity.end,
                    'ENTITY_CONFIDENCE': entity.confidence,
                    'ORIGINAL_TEXT': text
                })
                results.append(result)
        
        result_df = pd.DataFrame(results)
        logger.info(f"Extracted {len(result_df)} entities from {len(df)} records")
        
        return result_df


def main():
    """
    Main function to demonstrate NER extraction.
    """
    # Initialize NER system
    ner = ADEDrugNER()
    
    # Test with sample text
    sample_text = """
    Patient received Pfizer COVID-19 vaccine and experienced severe headache, 
    injection site pain, and mild fever lasting 2 days. Also reported shortness 
    of breath and chest pain.
    """
    
    print("Sample text:", sample_text)
    print("\nExtracting entities...")
    
    entities = ner.extract_entities(sample_text, use_biobert=False)
    
    print(f"\nFound {len(entities)} entities:")
    for entity in entities:
        print(f"- {entity.label}: '{entity.text}' (confidence: {entity.confidence:.2f})")


if __name__ == "__main__":
    main()
